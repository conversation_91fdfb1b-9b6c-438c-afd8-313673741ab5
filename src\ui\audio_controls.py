"""
Advanced Audio Controls Widget
Professional audio control interface with equalizer and effects
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider, QPushButton,
    QGroupBox, QComboBox, QCheckBox, QDial, QProgressBar, QFrame
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QPainter, QColor, QPen
import math


class VolumeButton(QPushButton):
    """Custom volume button with dynamic icon"""
    
    volume_changed = Signal(int)
    
    def __init__(self):
        super().__init__()
        self.volume_level = 70
        self.is_muted = False
        self.setFixedSize(40, 40)
        self.clicked.connect(self.toggle_mute)
        self.update_icon()
    
    def set_volume(self, volume):
        """Set volume level and update icon"""
        self.volume_level = volume
        self.is_muted = volume == 0
        self.update_icon()
    
    def toggle_mute(self):
        """Toggle mute state"""
        self.is_muted = not self.is_muted
        if self.is_muted:
            self.volume_changed.emit(0)
        else:
            self.volume_changed.emit(self.volume_level)
        self.update_icon()
    
    def update_icon(self):
        """Update button icon based on volume level"""
        if self.is_muted or self.volume_level == 0:
            icon = "🔇"
            tooltip = "Unmute"
        elif self.volume_level < 30:
            icon = "🔈"
            tooltip = f"Volume: {self.volume_level}% (Low)"
        elif self.volume_level < 70:
            icon = "🔉"
            tooltip = f"Volume: {self.volume_level}% (Medium)"
        else:
            icon = "🔊"
            tooltip = f"Volume: {self.volume_level}% (High)"
        
        self.setText(icon)
        self.setToolTip(tooltip)


class EqualizerWidget(QWidget):
    """Professional equalizer widget"""
    
    def __init__(self):
        super().__init__()
        self.bands = [60, 170, 310, 600, 1000, 3000, 6000, 12000, 14000, 16000]
        self.values = [0] * len(self.bands)  # dB values (-12 to +12)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup equalizer UI"""
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("10-Band Equalizer")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title)
        
        # Equalizer sliders
        eq_layout = QHBoxLayout()
        self.sliders = []
        
        for i, freq in enumerate(self.bands):
            band_layout = QVBoxLayout()
            
            # Frequency label
            if freq >= 1000:
                freq_text = f"{freq//1000}K"
            else:
                freq_text = str(freq)
            
            freq_label = QLabel(freq_text)
            freq_label.setAlignment(Qt.AlignCenter)
            freq_label.setFont(QFont("Arial", 8))
            
            # Slider
            slider = QSlider(Qt.Vertical)
            slider.setRange(-120, 120)  # -12dB to +12dB (x10 for precision)
            slider.setValue(0)
            slider.setFixedHeight(120)
            slider.valueChanged.connect(lambda v, idx=i: self.band_changed(idx, v))
            self.sliders.append(slider)
            
            # Value label
            value_label = QLabel("0")
            value_label.setAlignment(Qt.AlignCenter)
            value_label.setFont(QFont("Arial", 8))
            value_label.setFixedWidth(30)
            
            band_layout.addWidget(freq_label)
            band_layout.addWidget(slider)
            band_layout.addWidget(value_label)
            
            eq_layout.addLayout(band_layout)
        
        layout.addLayout(eq_layout)
        
        # Presets
        preset_layout = QHBoxLayout()
        
        preset_label = QLabel("Presets:")
        preset_layout.addWidget(preset_label)
        
        self.preset_combo = QComboBox()
        self.preset_combo.addItems([
            "Flat", "Rock", "Pop", "Jazz", "Classical", 
            "Electronic", "Hip-Hop", "Vocal", "Bass Boost"
        ])
        self.preset_combo.currentTextChanged.connect(self.apply_preset)
        preset_layout.addWidget(self.preset_combo)
        
        reset_btn = QPushButton("Reset")
        reset_btn.clicked.connect(self.reset_equalizer)
        preset_layout.addWidget(reset_btn)
        
        layout.addLayout(preset_layout)
    
    def band_changed(self, band_index, value):
        """Handle equalizer band change"""
        db_value = value / 10.0  # Convert back to dB
        self.values[band_index] = db_value
        
        # Update value label
        value_label = self.sliders[band_index].parent().layout().itemAt(2).widget()
        value_label.setText(f"{db_value:+.1f}")
    
    def apply_preset(self, preset_name):
        """Apply equalizer preset"""
        presets = {
            "Flat": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            "Rock": [5, 4, -1, -2, -1, 1, 3, 4, 4, 4],
            "Pop": [-1, 2, 4, 4, 1, -1, -1, 1, 2, 2],
            "Jazz": [4, 3, 1, 2, -1, -1, 0, 1, 2, 3],
            "Classical": [5, 4, 3, 2, -1, -1, 0, 2, 3, 4],
            "Electronic": [5, 4, 1, 0, -1, 2, 1, 2, 4, 5],
            "Hip-Hop": [5, 4, 1, 3, -1, -1, 1, 2, 3, 4],
            "Vocal": [-2, -1, 2, 4, 4, 3, 2, 1, 0, -1],
            "Bass Boost": [7, 6, 4, 2, 0, -1, -1, 0, 1, 2]
        }
        
        if preset_name in presets:
            values = presets[preset_name]
            for i, value in enumerate(values):
                self.sliders[i].setValue(int(value * 10))
    
    def reset_equalizer(self):
        """Reset all bands to 0"""
        for slider in self.sliders:
            slider.setValue(0)


class AudioControlsWidget(QWidget):
    """Complete audio controls widget"""
    
    volume_changed = Signal(int)
    mute_toggled = Signal(bool)
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """Setup audio controls UI"""
        layout = QVBoxLayout(self)
        
        # Main volume controls
        volume_group = QGroupBox("Volume Control")
        volume_layout = QHBoxLayout(volume_group)
        
        # Volume button
        self.volume_button = VolumeButton()
        self.volume_button.volume_changed.connect(self.volume_changed.emit)
        volume_layout.addWidget(self.volume_button)
        
        # Volume slider
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.valueChanged.connect(self.on_volume_changed)
        volume_layout.addWidget(self.volume_slider)
        
        # Volume percentage
        self.volume_label = QLabel("70%")
        self.volume_label.setMinimumWidth(40)
        volume_layout.addWidget(self.volume_label)
        
        # Volume boost checkbox
        self.volume_boost = QCheckBox("Boost (200%)")
        self.volume_boost.toggled.connect(self.toggle_volume_boost)
        volume_layout.addWidget(self.volume_boost)
        
        layout.addWidget(volume_group)
        
        # Audio effects
        effects_group = QGroupBox("Audio Effects")
        effects_layout = QVBoxLayout(effects_group)
        
        # Effects checkboxes
        effects_row1 = QHBoxLayout()
        self.bass_boost = QCheckBox("Bass Boost")
        self.treble_boost = QCheckBox("Treble Boost")
        self.surround = QCheckBox("Virtual Surround")
        
        effects_row1.addWidget(self.bass_boost)
        effects_row1.addWidget(self.treble_boost)
        effects_row1.addWidget(self.surround)
        effects_layout.addLayout(effects_row1)
        
        effects_row2 = QHBoxLayout()
        self.noise_reduction = QCheckBox("Noise Reduction")
        self.voice_enhance = QCheckBox("Voice Enhancement")
        self.normalize = QCheckBox("Audio Normalize")
        
        effects_row2.addWidget(self.noise_reduction)
        effects_row2.addWidget(self.voice_enhance)
        effects_row2.addWidget(self.normalize)
        effects_layout.addLayout(effects_row2)
        
        layout.addWidget(effects_group)
        
        # Equalizer
        self.equalizer = EqualizerWidget()
        layout.addWidget(self.equalizer)
        
        # Audio info
        info_group = QGroupBox("Audio Information")
        info_layout = QVBoxLayout(info_group)
        
        self.audio_info = QLabel("No audio loaded")
        self.audio_info.setWordWrap(True)
        info_layout.addWidget(self.audio_info)
        
        layout.addWidget(info_group)
    
    def on_volume_changed(self, value):
        """Handle volume slider change"""
        self.volume_label.setText(f"{value}%")
        self.volume_button.set_volume(value)
        self.volume_changed.emit(value)
    
    def toggle_volume_boost(self, enabled):
        """Toggle volume boost mode"""
        if enabled:
            self.volume_slider.setRange(0, 200)
            self.volume_slider.setStyleSheet("QSlider::handle:horizontal { background-color: #E74C3C; }")
        else:
            self.volume_slider.setRange(0, 100)
            self.volume_slider.setStyleSheet("")
            if self.volume_slider.value() > 100:
                self.volume_slider.setValue(100)
    
    def update_audio_info(self, info):
        """Update audio information display"""
        self.audio_info.setText(info)
    
    def apply_styles(self):
        """Apply custom styles"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: 600;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                margin: 5px 0;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QSlider::groove:horizontal {
                border: 1px solid #BDC3C7;
                height: 8px;
                background: #FFFFFF;
                border-radius: 4px;
            }
            
            QSlider::handle:horizontal {
                background: #3498DB;
                border: 2px solid #2980B9;
                width: 20px;
                margin: -6px 0;
                border-radius: 10px;
            }
            
            QSlider::sub-page:horizontal {
                background: #3498DB;
                border-radius: 4px;
            }
            
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid #BDC3C7;
            }
            
            QCheckBox::indicator:checked {
                background-color: #3498DB;
                border-color: #2980B9;
            }
        """)
