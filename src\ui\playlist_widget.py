"""
Professional Playlist Widget for VoiceSubMaster
Enhanced playlist management with drag & drop support
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QFileDialog, QMenu, QMessageBox
)
from PySide6.QtCore import Qt, Signal, QMimeData, QUrl
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QAction
from .icons import icon_manager
import os
from pathlib import Path


class PlaylistWidget(QWidget):
    """Professional playlist widget with file management"""
    
    file_selected = Signal(str)  # Signal when file is selected for playback
    playlist_changed = Signal(list)  # Signal when playlist changes
    
    def __init__(self):
        super().__init__()
        self.playlist_files = []
        self.current_index = -1
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """Setup playlist UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Header with title and controls
        header_layout = QHBoxLayout()
        
        # Title
        title_label = QLabel("📋 Playlist")
        title_label.setStyleSheet("font-weight: 600; font-size: 14px; color: #2C3E50;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Add files button
        self.add_button = QPushButton()
        self.add_button.setIcon(icon_manager.get_icon("folder_open", 20, "#27AE60"))
        self.add_button.setText(" Add Files")
        self.add_button.setToolTip("Add media files to playlist")
        self.add_button.clicked.connect(self.add_files)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #2ECC71;
            }
        """)
        header_layout.addWidget(self.add_button)
        
        # Clear playlist button
        self.clear_button = QPushButton()
        self.clear_button.setIcon(icon_manager.get_icon("stop", 16, "#E74C3C"))
        self.clear_button.setText("")
        self.clear_button.setToolTip("Clear playlist")
        self.clear_button.clicked.connect(self.clear_playlist)
        self.clear_button.setFixedSize(32, 32)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #EC7063;
            }
        """)
        header_layout.addWidget(self.clear_button)
        
        layout.addLayout(header_layout)
        
        # Playlist list
        self.playlist_list = QListWidget()
        self.playlist_list.setAcceptDrops(True)
        self.playlist_list.setDragDropMode(QListWidget.InternalMove)
        self.playlist_list.itemDoubleClicked.connect(self.play_selected_file)
        self.playlist_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.playlist_list.customContextMenuRequested.connect(self.show_context_menu)
        layout.addWidget(self.playlist_list)
        
        # Playlist info
        self.info_label = QLabel("No files in playlist")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet("color: #7F8C8D; font-style: italic; padding: 10px;")
        layout.addWidget(self.info_label)
        
        # Playback controls
        controls_layout = QHBoxLayout()
        
        # Previous button
        self.prev_button = QPushButton()
        self.prev_button.setIcon(icon_manager.get_icon("skip_previous", 16))
        self.prev_button.setToolTip("Previous file")
        self.prev_button.clicked.connect(self.play_previous)
        self.prev_button.setFixedSize(32, 32)
        controls_layout.addWidget(self.prev_button)
        
        # Next button
        self.next_button = QPushButton()
        self.next_button.setIcon(icon_manager.get_icon("skip_next", 16))
        self.next_button.setToolTip("Next file")
        self.next_button.clicked.connect(self.play_next)
        self.next_button.setFixedSize(32, 32)
        controls_layout.addWidget(self.next_button)
        
        controls_layout.addStretch()
        
        # Shuffle button
        self.shuffle_button = QPushButton("🔀")
        self.shuffle_button.setToolTip("Shuffle playlist")
        self.shuffle_button.setCheckable(True)
        self.shuffle_button.setFixedSize(32, 32)
        controls_layout.addWidget(self.shuffle_button)
        
        # Repeat button
        self.repeat_button = QPushButton("🔁")
        self.repeat_button.setToolTip("Repeat playlist")
        self.repeat_button.setCheckable(True)
        self.repeat_button.setFixedSize(32, 32)
        controls_layout.addWidget(self.repeat_button)
        
        layout.addLayout(controls_layout)
        
        # Update button states
        self.update_controls()
    
    def add_files(self):
        """Add files to playlist"""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFiles)  # Multiple files
        file_dialog.setNameFilter(
            "Media Files (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v "
            "*.mp3 *.wav *.flac *.aac *.ogg *.wma *.m4a);;All Files (*)"
        )
        
        if file_dialog.exec():
            files = file_dialog.selectedFiles()
            self.add_files_to_playlist(files)
    
    def add_files_to_playlist(self, files):
        """Add multiple files to playlist"""
        added_count = 0
        for file_path in files:
            if os.path.exists(file_path) and file_path not in self.playlist_files:
                self.playlist_files.append(file_path)
                
                # Add to list widget
                file_name = Path(file_path).name
                item = QListWidgetItem(file_name)
                item.setData(Qt.UserRole, file_path)
                item.setToolTip(file_path)
                self.playlist_list.addItem(item)
                
                added_count += 1
        
        if added_count > 0:
            self.update_info()
            self.update_controls()
            self.playlist_changed.emit(self.playlist_files)
    
    def clear_playlist(self):
        """Clear all files from playlist"""
        if self.playlist_files:
            reply = QMessageBox.question(
                self, "Clear Playlist", 
                "Are you sure you want to clear the playlist?",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.playlist_files.clear()
                self.playlist_list.clear()
                self.current_index = -1
                self.update_info()
                self.update_controls()
                self.playlist_changed.emit(self.playlist_files)
    
    def play_selected_file(self, item):
        """Play selected file from playlist"""
        file_path = item.data(Qt.UserRole)
        self.current_index = self.playlist_files.index(file_path)
        self.highlight_current_item()
        self.file_selected.emit(file_path)
    
    def play_previous(self):
        """Play previous file in playlist"""
        if self.playlist_files and self.current_index > 0:
            self.current_index -= 1
            self.highlight_current_item()
            self.file_selected.emit(self.playlist_files[self.current_index])
    
    def play_next(self):
        """Play next file in playlist"""
        if self.playlist_files:
            if self.current_index < len(self.playlist_files) - 1:
                self.current_index += 1
            elif self.repeat_button.isChecked():
                self.current_index = 0
            else:
                return
            
            self.highlight_current_item()
            self.file_selected.emit(self.playlist_files[self.current_index])
    
    def highlight_current_item(self):
        """Highlight currently playing item"""
        for i in range(self.playlist_list.count()):
            item = self.playlist_list.item(i)
            if i == self.current_index:
                item.setBackground(Qt.lightGray)
                item.setText(f"▶️ {Path(self.playlist_files[i]).name}")
            else:
                item.setBackground(Qt.transparent)
                item.setText(Path(self.playlist_files[i]).name)
    
    def show_context_menu(self, position):
        """Show context menu for playlist items"""
        item = self.playlist_list.itemAt(position)
        if item:
            menu = QMenu(self)
            
            play_action = QAction("Play", self)
            play_action.triggered.connect(lambda: self.play_selected_file(item))
            menu.addAction(play_action)
            
            menu.addSeparator()
            
            remove_action = QAction("Remove from playlist", self)
            remove_action.triggered.connect(lambda: self.remove_item(item))
            menu.addAction(remove_action)
            
            menu.exec(self.playlist_list.mapToGlobal(position))
    
    def remove_item(self, item):
        """Remove item from playlist"""
        file_path = item.data(Qt.UserRole)
        if file_path in self.playlist_files:
            index = self.playlist_files.index(file_path)
            self.playlist_files.remove(file_path)
            self.playlist_list.takeItem(self.playlist_list.row(item))
            
            # Adjust current index
            if index == self.current_index:
                self.current_index = -1
            elif index < self.current_index:
                self.current_index -= 1
            
            self.update_info()
            self.update_controls()
            self.playlist_changed.emit(self.playlist_files)
    
    def update_info(self):
        """Update playlist info label"""
        count = len(self.playlist_files)
        if count == 0:
            self.info_label.setText("No files in playlist")
        else:
            self.info_label.setText(f"{count} file{'s' if count != 1 else ''} in playlist")
    
    def update_controls(self):
        """Update control button states"""
        has_files = len(self.playlist_files) > 0
        self.prev_button.setEnabled(has_files and self.current_index > 0)
        self.next_button.setEnabled(has_files and self.current_index < len(self.playlist_files) - 1)
        self.clear_button.setEnabled(has_files)
    
    def set_current_file(self, file_path):
        """Set current playing file"""
        if file_path in self.playlist_files:
            self.current_index = self.playlist_files.index(file_path)
            self.highlight_current_item()
            self.update_controls()
    
    def apply_styles(self):
        """Apply custom styles"""
        self.setStyleSheet("""
            QListWidget {
                border: 1px solid #BDC3C7;
                border-radius: 6px;
                background-color: #FFFFFF;
                alternate-background-color: #F8F9F9;
                selection-background-color: #3498DB;
                selection-color: white;
                padding: 5px;
            }
            
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ECF0F1;
                border-radius: 3px;
                margin: 1px;
            }
            
            QListWidget::item:hover {
                background-color: #EBF5FB;
            }
            
            QListWidget::item:selected {
                background-color: #3498DB;
                color: white;
            }
            
            QPushButton {
                background-color: #F8F9F9;
                border: 1px solid #BDC3C7;
                border-radius: 6px;
                padding: 6px;
            }
            
            QPushButton:hover {
                background-color: #EBF5FB;
                border-color: #3498DB;
            }
            
            QPushButton:pressed {
                background-color: #D6EAF8;
            }
            
            QPushButton:checked {
                background-color: #3498DB;
                color: white;
                border-color: #2980B9;
            }
        """)
