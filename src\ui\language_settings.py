"""
Language Settings Widget for VoiceSubMaster
Multi-language support with professional interface
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QGroupBox, QFormLayout, QCheckBox, QButtonGroup
)
from PySide6.QtCore import Qt, Signal
from .icons import icon_manager


class LanguageSettingsWidget(QWidget):
    """Professional language settings widget"""
    
    language_changed = Signal(str)  # Signal when language changes
    mode_changed = Signal(str)      # Signal when mode changes
    
    def __init__(self):
        super().__init__()
        self.current_language = "English"
        self.current_mode = "text"
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """Setup language settings UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # Title
        title_label = QLabel("⚙️ Language & Interface Settings")
        title_label.setStyleSheet("font-weight: 700; font-size: 16px; color: #2C3E50; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Interface Language Group
        interface_group = QGroupBox("Interface Language")
        interface_layout = QVBoxLayout(interface_group)
        
        # Language selection with flags
        lang_layout = QHBoxLayout()
        
        # Create language buttons
        self.language_buttons = QButtonGroup(self)
        
        languages = [
            ("English", "🇺🇸", "en"),
            ("العربية", "🇸🇦", "ar"), 
            ("Français", "🇫🇷", "fr"),
            ("Español", "🇪🇸", "es")
        ]
        
        for i, (name, flag, code) in enumerate(languages):
            btn = QPushButton(f"{flag} {name}")
            btn.setCheckable(True)
            btn.setProperty("language_code", code)
            btn.clicked.connect(lambda checked, c=code: self.set_language(c))
            
            if code == "en":  # Default to English
                btn.setChecked(True)
            
            self.language_buttons.addButton(btn, i)
            lang_layout.addWidget(btn)
        
        interface_layout.addLayout(lang_layout)
        
        # Language description
        self.lang_description = QLabel("Select your preferred interface language")
        self.lang_description.setAlignment(Qt.AlignCenter)
        self.lang_description.setStyleSheet("color: #7F8C8D; font-style: italic; padding: 5px;")
        interface_layout.addWidget(self.lang_description)
        
        layout.addWidget(interface_group)
        
        # Input Mode Group
        mode_group = QGroupBox("Input Mode")
        mode_layout = QVBoxLayout(mode_group)
        
        # Mode selection
        mode_btn_layout = QHBoxLayout()
        
        self.mode_buttons = QButtonGroup(self)
        
        # Text mode button
        self.text_btn = QPushButton("📝 Text Mode")
        self.text_btn.setCheckable(True)
        self.text_btn.setChecked(True)  # Default
        self.text_btn.clicked.connect(lambda: self.set_mode("text"))
        self.mode_buttons.addButton(self.text_btn, 0)
        mode_btn_layout.addWidget(self.text_btn)
        
        # Voice mode button
        self.voice_btn = QPushButton("🎤 Voice Mode")
        self.voice_btn.setCheckable(True)
        self.voice_btn.clicked.connect(lambda: self.set_mode("voice"))
        self.mode_buttons.addButton(self.voice_btn, 1)
        mode_btn_layout.addWidget(self.voice_btn)
        
        mode_layout.addLayout(mode_btn_layout)
        
        # Mode description
        self.mode_description = QLabel("Text mode: Type or paste text for translation\\nVoice mode: Use microphone for speech input")
        self.mode_description.setAlignment(Qt.AlignCenter)
        self.mode_description.setStyleSheet("color: #7F8C8D; font-style: italic; padding: 5px;")
        mode_layout.addWidget(self.mode_description)
        
        layout.addWidget(mode_group)
        
        # Advanced Settings Group
        advanced_group = QGroupBox("Advanced Language Settings")
        advanced_layout = QFormLayout(advanced_group)
        
        # Auto-detect language
        self.auto_detect = QCheckBox("Auto-detect input language")
        self.auto_detect.setChecked(True)
        advanced_layout.addRow("Detection:", self.auto_detect)
        
        # Default translation direction
        self.translation_direction = QComboBox()
        self.translation_direction.addItems([
            "Auto → English",
            "Auto → Arabic", 
            "Auto → French",
            "Auto → Spanish",
            "English → Arabic",
            "Arabic → English",
            "French → English",
            "Spanish → English"
        ])
        advanced_layout.addRow("Default Translation:", self.translation_direction)
        
        # Voice settings
        self.voice_language = QComboBox()
        self.voice_language.addItems([
            "Auto-detect",
            "English (US)",
            "Arabic (Standard)",
            "French (France)", 
            "Spanish (Spain)"
        ])
        advanced_layout.addRow("Voice Recognition:", self.voice_language)
        
        layout.addWidget(advanced_group)
        
        # Welcome Messages Group
        welcome_group = QGroupBox("Welcome Messages")
        welcome_layout = QVBoxLayout(welcome_group)
        
        # Current welcome messages
        self.welcome_messages = {
            "en": "Hello, how can I help you today?",
            "ar": "مرحباً، كيف يمكن مساعدتك اليوم؟",
            "fr": "Bonjour, comment puis-je vous aider aujourd'hui?",
            "es": "Hola, ¿cómo puedo ayudarte hoy?"
        }
        
        self.welcome_display = QLabel()
        self.welcome_display.setAlignment(Qt.AlignCenter)
        self.welcome_display.setStyleSheet("""
            background-color: #F8F9F9;
            border: 1px solid #BDC3C7;
            border-radius: 8px;
            padding: 15px;
            font-size: 14px;
            color: #2C3E50;
        """)
        welcome_layout.addWidget(self.welcome_display)
        
        layout.addWidget(welcome_group)
        
        layout.addStretch()
        
        # Update display
        self.update_welcome_display()
    
    def set_language(self, language_code):
        """Set interface language"""
        self.current_language = language_code
        self.language_changed.emit(language_code)
        self.update_welcome_display()
        self.update_descriptions()
    
    def set_mode(self, mode):
        """Set input mode"""
        self.current_mode = mode
        self.mode_changed.emit(mode)
    
    def update_welcome_display(self):
        """Update welcome message display"""
        message = self.welcome_messages.get(self.current_language, self.welcome_messages["en"])
        
        # Get language name
        lang_names = {"en": "English", "ar": "العربية", "fr": "Français", "es": "Español"}
        lang_name = lang_names.get(self.current_language, "English")
        
        self.welcome_display.setText(f"[{lang_name}] {message}")
    
    def update_descriptions(self):
        """Update UI descriptions based on selected language"""
        descriptions = {
            "en": {
                "lang_desc": "Select your preferred interface language",
                "mode_desc": "Text mode: Type or paste text for translation\\nVoice mode: Use microphone for speech input"
            },
            "ar": {
                "lang_desc": "اختر لغة الواجهة المفضلة لديك",
                "mode_desc": "وضع النص: اكتب أو الصق النص للترجمة\\nوضع الصوت: استخدم الميكروفون لإدخال الكلام"
            },
            "fr": {
                "lang_desc": "Sélectionnez votre langue d'interface préférée",
                "mode_desc": "Mode texte: Tapez ou collez le texte à traduire\\nMode vocal: Utilisez le microphone pour la saisie vocale"
            },
            "es": {
                "lang_desc": "Selecciona tu idioma de interfaz preferido",
                "mode_desc": "Modo texto: Escribe o pega texto para traducir\\nModo voz: Usa el micrófono para entrada de voz"
            }
        }
        
        desc = descriptions.get(self.current_language, descriptions["en"])
        self.lang_description.setText(desc["lang_desc"])
        self.mode_description.setText(desc["mode_desc"])
    
    def get_current_settings(self):
        """Get current language settings"""
        return {
            "language": self.current_language,
            "mode": self.current_mode,
            "auto_detect": self.auto_detect.isChecked(),
            "translation_direction": self.translation_direction.currentText(),
            "voice_language": self.voice_language.currentText()
        }
    
    def apply_styles(self):
        """Apply custom styles"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: 600;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                margin: 10px 0;
                padding-top: 15px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2C3E50;
                font-size: 13px;
            }
            
            QPushButton {
                background-color: #FFFFFF;
                color: #2C3E50;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: 600;
                font-size: 13px;
                min-height: 20px;
                min-width: 120px;
            }
            
            QPushButton:hover {
                background-color: #EBF5FB;
                border-color: #3498DB;
            }
            
            QPushButton:checked {
                background-color: #3498DB;
                color: #FFFFFF;
                border-color: #2980B9;
            }
            
            QComboBox {
                border: 1px solid #BDC3C7;
                border-radius: 6px;
                padding: 8px;
                background-color: #FFFFFF;
                min-width: 150px;
            }
            
            QComboBox:hover {
                border-color: #3498DB;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid #BDC3C7;
                background-color: #FFFFFF;
            }
            
            QCheckBox::indicator:checked {
                background-color: #3498DB;
                border-color: #2980B9;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
        """)
