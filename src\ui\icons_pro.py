"""
Professional Icon System for VoiceSubMaster
Ultra-modern SVG icons with gradient support and animations
"""

from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QBrush, QLinearGradient
from PySide6.QtCore import Qt, QSize, QRect
from PySide6.QtSvg import QSvgRenderer
import xml.etree.ElementTree as ET


class ProfessionalIconManager:
    """Advanced icon manager with gradient and animation support"""
    
    def __init__(self):
        self.icon_cache = {}
        self.gradient_cache = {}
    
    def get_icon(self, icon_name, size=24, color="#ffffff", gradient=None):
        """Get a professional icon with optional gradient"""
        cache_key = f"{icon_name}_{size}_{color}_{gradient}"
        
        if cache_key not in self.icon_cache:
            svg_data = self.get_professional_svg(icon_name, color, gradient)
            if svg_data:
                self.icon_cache[cache_key] = self.create_icon_from_svg(svg_data, size)
            else:
                # Fallback to emoji icon
                self.icon_cache[cache_key] = self.create_emoji_icon(icon_name, size)
        
        return self.icon_cache[cache_key]
    
    def get_professional_svg(self, icon_name, color, gradient=None):
        """Get professional SVG with gradients and modern styling"""
        
        # Define gradient if provided
        gradient_def = ""
        fill_color = color
        
        if gradient:
            gradient_id = f"grad_{icon_name}"
            if gradient == "blue":
                gradient_def = f'''
                <defs>
                    <linearGradient id="{gradient_id}" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                    </linearGradient>
                </defs>'''
                fill_color = f"url(#{gradient_id})"
            elif gradient == "green":
                gradient_def = f'''
                <defs>
                    <linearGradient id="{gradient_id}" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#00f260;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#0575e6;stop-opacity:1" />
                    </linearGradient>
                </defs>'''
                fill_color = f"url(#{gradient_id})"
            elif gradient == "red":
                gradient_def = f'''
                <defs>
                    <linearGradient id="{gradient_id}" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#ee5a24;stop-opacity:1" />
                    </linearGradient>
                </defs>'''
                fill_color = f"url(#{gradient_id})"
        
        icons = {
            "play": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <path d="M8 5v14l11-7z" fill="{fill_color}" stroke="none"/>
                <circle cx="12" cy="12" r="11" fill="none" stroke="{color}" stroke-width="0.5" opacity="0.3"/>
            </svg>''',
            
            "pause": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <rect x="6" y="4" width="4" height="16" rx="2" fill="{fill_color}"/>
                <rect x="14" y="4" width="4" height="16" rx="2" fill="{fill_color}"/>
                <circle cx="12" cy="12" r="11" fill="none" stroke="{color}" stroke-width="0.5" opacity="0.3"/>
            </svg>''',
            
            "stop": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <rect x="6" y="6" width="12" height="12" rx="2" fill="{fill_color}"/>
                <circle cx="12" cy="12" r="11" fill="none" stroke="{color}" stroke-width="0.5" opacity="0.3"/>
            </svg>''',
            
            "skip_previous": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <rect x="6" y="4" width="2" height="16" rx="1" fill="{fill_color}"/>
                <path d="M9.5 12l8.5 6V6z" fill="{fill_color}"/>
                <circle cx="12" cy="12" r="11" fill="none" stroke="{color}" stroke-width="0.5" opacity="0.2"/>
            </svg>''',
            
            "skip_next": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <rect x="16" y="4" width="2" height="16" rx="1" fill="{fill_color}"/>
                <path d="M6 18l8.5-6L6 6v12z" fill="{fill_color}"/>
                <circle cx="12" cy="12" r="11" fill="none" stroke="{color}" stroke-width="0.5" opacity="0.2"/>
            </svg>''',
            
            "volume_up": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <path d="M3 9v6h4l5 5V4L7 9H3z" fill="{fill_color}"/>
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z" fill="{fill_color}" opacity="0.8"/>
                <path d="M14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" fill="{fill_color}" opacity="0.6"/>
            </svg>''',
            
            "volume_down": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <path d="M3 9v6h4l5 5V4L7 9H3z" fill="{fill_color}"/>
                <path d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z" fill="{fill_color}" opacity="0.7"/>
            </svg>''',
            
            "volume_off": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z" fill="{fill_color}"/>
            </svg>''',
            
            "fullscreen": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" fill="{fill_color}"/>
                <rect x="2" y="2" width="20" height="20" rx="2" fill="none" stroke="{color}" stroke-width="0.5" opacity="0.3"/>
            </svg>''',
            
            "settings": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z" fill="{fill_color}"/>
            </svg>''',
            
            "folder_open": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <path d="M20 6h-2l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10z" fill="{fill_color}"/>
                <path d="M2 6h8l2 2h8" fill="none" stroke="{color}" stroke-width="1" opacity="0.5"/>
            </svg>''',
            
            "playlist": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <rect x="3" y="6" width="12" height="2" rx="1" fill="{fill_color}"/>
                <rect x="3" y="10" width="12" height="2" rx="1" fill="{fill_color}"/>
                <rect x="3" y="14" width="8" height="2" rx="1" fill="{fill_color}"/>
                <circle cx="17" cy="15" r="3" fill="{fill_color}" opacity="0.8"/>
                <rect x="16" y="6" width="2" height="8" rx="1" fill="{fill_color}"/>
                <path d="M18 6h3v2h-3z" fill="{fill_color}" opacity="0.6"/>
            </svg>''',
            
            "equalizer": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <rect x="3" y="12" width="2" height="8" rx="1" fill="{fill_color}"/>
                <rect x="7" y="8" width="2" height="12" rx="1" fill="{fill_color}"/>
                <rect x="11" y="4" width="2" height="16" rx="1" fill="{fill_color}"/>
                <rect x="15" y="10" width="2" height="10" rx="1" fill="{fill_color}"/>
                <rect x="19" y="6" width="2" height="14" rx="1" fill="{fill_color}"/>
            </svg>''',
            
            "language": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <circle cx="12" cy="12" r="10" fill="none" stroke="{fill_color}" stroke-width="2"/>
                <path d="M2 12h20M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" fill="none" stroke="{fill_color}" stroke-width="1.5"/>
                <text x="12" y="16" text-anchor="middle" fill="{fill_color}" font-size="8" font-weight="bold">Aa</text>
            </svg>''',
            
            "microphone": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <rect x="9" y="2" width="6" height="12" rx="3" fill="{fill_color}"/>
                <path d="M5 10v2a7 7 0 0 0 14 0v-2" fill="none" stroke="{fill_color}" stroke-width="2"/>
                <line x1="12" y1="19" x2="12" y2="23" stroke="{fill_color}" stroke-width="2"/>
                <line x1="8" y1="23" x2="16" y2="23" stroke="{fill_color}" stroke-width="2"/>
            </svg>''',
            
            "add_circle": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <circle cx="12" cy="12" r="10" fill="{fill_color}"/>
                <line x1="12" y1="8" x2="12" y2="16" stroke="white" stroke-width="2"/>
                <line x1="8" y1="12" x2="16" y2="12" stroke="white" stroke-width="2"/>
            </svg>''',
            
            "shuffle": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <path d="M10.59 9.17L5.41 4 4 5.41l5.17 5.17 1.42-1.41zM14.5 4l2.04 2.04L4 18.59 5.41 20 17.96 7.46 20 9.5V4h-5.5zm.33 9.41l-1.41 1.41 3.13 3.13L14.5 20H20v-5.5l-2.04 2.04-3.13-3.13z" fill="{fill_color}"/>
            </svg>''',
            
            "repeat": f'''<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                {gradient_def}
                <path d="M7 7h10v3l4-4-4-4v3H5v6h2V7zm10 10H7v-3l-4 4 4 4v-3h12v-6h-2v4z" fill="{fill_color}"/>
            </svg>'''
        }
        
        return icons.get(icon_name)
    
    def create_icon_from_svg(self, svg_data, size):
        """Create QIcon from SVG data with ultra-high quality rendering"""
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())

        # Create ultra high-DPI pixmap for crisp rendering
        scale_factor = 4  # 4x for ultra crisp icons
        pixmap = QPixmap(size * scale_factor, size * scale_factor)
        pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        # Enable all quality rendering hints
        painter.setRenderHint(QPainter.Antialiasing, True)
        painter.setRenderHint(QPainter.SmoothPixmapTransform, True)
        painter.setRenderHint(QPainter.TextAntialiasing, True)
        painter.setRenderHint(QPainter.LosslessImageRendering, True)

        # Render at high resolution
        renderer.render(painter)
        painter.end()

        # Scale down with highest quality for crisp final result
        final_pixmap = pixmap.scaled(
            size, size,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )

        return QIcon(final_pixmap)
    
    def create_emoji_icon(self, icon_name, size):
        """Create fallback emoji icon"""
        emoji_map = {
            "play": "▶️",
            "pause": "⏸️",
            "stop": "⏹️",
            "skip_previous": "⏮️",
            "skip_next": "⏭️",
            "volume_up": "🔊",
            "volume_down": "🔉",
            "volume_off": "🔇",
            "fullscreen": "⛶",
            "settings": "⚙️",
            "folder_open": "📁",
            "playlist": "📋",
            "equalizer": "🎚️",
            "language": "🌐",
            "microphone": "🎤",
            "add_circle": "➕",
            "shuffle": "🔀",
            "repeat": "🔁"
        }
        
        emoji = emoji_map.get(icon_name, "❓")
        
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing, True)
        painter.setPen(QColor("#ffffff"))
        font = painter.font()
        font.setPixelSize(int(size * 0.7))
        painter.setFont(font)
        painter.drawText(pixmap.rect(), Qt.AlignCenter, emoji)
        painter.end()
        
        return QIcon(pixmap)


# Global professional icon manager instance
pro_icon_manager = ProfessionalIconManager()
