"""
Test cases for MainWindow functionality
"""

import sys
import unittest
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from PySide6.QtWidgets import QApplication
from PySide6.QtTest import QTest
from PySide6.QtCore import Qt
from src.ui.main_window import MainWindow


class TestMainWindow(unittest.TestCase):
    """Test cases for MainWindow class"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """Set up test case"""
        self.window = MainWindow()
    
    def tearDown(self):
        """Clean up test case"""
        self.window.close()
    
    def test_window_creation(self):
        """Test that window is created successfully"""
        self.assertIsNotNone(self.window)
        self.assertEqual(self.window.windowTitle(), "VoiceSubMaster - Professional Media Player")
    
    def test_media_player_initialization(self):
        """Test that media player is initialized"""
        self.assertIsNotNone(self.window.media_player)
        self.assertIsNotNone(self.window.audio_output)
        self.assertIsNotNone(self.window.video_widget)
    
    def test_controls_initialization(self):
        """Test that control buttons are initialized"""
        self.assertIsNotNone(self.window.play_button)
        self.assertIsNotNone(self.window.pause_button)
        self.assertIsNotNone(self.window.volume_slider)
        self.assertIsNotNone(self.window.position_slider)
    
    def test_volume_control(self):
        """Test volume control functionality"""
        # Test setting volume
        self.window.volume_slider.setValue(50)
        self.window.set_volume(50)
        self.assertEqual(self.window.volume_slider.value(), 50)

        # Test volume up
        self.window.volume_up()
        self.assertEqual(self.window.volume_slider.value(), 60)

        # Test volume down
        self.window.volume_down()
        self.assertEqual(self.window.volume_slider.value(), 50)
    
    def test_time_formatting(self):
        """Test time formatting function"""
        # Test various time formats
        self.assertEqual(self.window.format_time(0), "00:00")
        self.assertEqual(self.window.format_time(60000), "01:00")
        self.assertEqual(self.window.format_time(125000), "02:05")


if __name__ == "__main__":
    unittest.main()
