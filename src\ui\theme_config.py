"""
Theme configuration for VoiceSubMaster
Based on modern flat design principles
"""

class ThemeColors:
    """Color palette for the application"""
    
    # Primary colors
    PRIMARY_BACKGROUND = "#ECF0F1"      # Light gray background
    PRIMARY_TEXT = "#2C3E50"            # Dark blue-gray text
    SECONDARY_TEXT = "#5D6D7E"          # Medium gray text
    
    # Accent colors
    ACCENT_BLUE = "#3498DB"             # Primary blue
    ACCENT_BLUE_DARK = "#2980B9"        # Darker blue for hover
    ACCENT_BLUE_LIGHT = "#5DADE2"       # Lighter blue
    
    # Button colors
    SUCCESS_GREEN = "#27AE60"           # Play button
    SUCCESS_GREEN_HOVER = "#2ECC71"     # Play button hover
    DANGER_RED = "#E74C3C"              # Pause/Stop button
    DANGER_RED_HOVER = "#EC7063"        # Pause button hover
    
    # Container colors
    CONTAINER_WHITE = "#FFFFFF"         # White containers
    CONTAINER_WARM_WHITE = "#F8F9F9"    # Warm white for menu/status
    BORDER_GRAY = "#BDC3C7"             # Border color
    BORDER_LIGHT = "#D5DBDB"            # Light border for disabled
    
    # Language specific colors
    ENGLISH_BUTTON = "#3498DB"          # English button blue
    ARABIC_BUTTON = "#E74C3C"           # Arabic button red


class ThemeFonts:
    """Font configuration"""
    
    # Primary fonts
    PRIMARY_FONT = "'Segoe UI', Arial, sans-serif"
    MONOSPACE_FONT = "'Consolas', 'Courier New', monospace"
    
    # Language specific fonts
    ENGLISH_FONT = "'Inter', 'Segoe UI', sans-serif"
    ARABIC_FONT = "'Amiri', 'Traditional Arabic', serif"
    
    # Font sizes
    TITLE_SIZE = "18px"
    SUBTITLE_SIZE = "16px"
    NORMAL_SIZE = "14px"
    SMALL_SIZE = "12px"


class ThemeMetrics:
    """Spacing and sizing configuration"""
    
    # Border radius
    BORDER_RADIUS_LARGE = "12px"
    BORDER_RADIUS_MEDIUM = "8px"
    BORDER_RADIUS_SMALL = "4px"
    BORDER_RADIUS_ROUND = "50%"
    
    # Spacing
    SPACING_LARGE = "20px"
    SPACING_MEDIUM = "15px"
    SPACING_SMALL = "10px"
    SPACING_TINY = "5px"
    
    # Padding
    PADDING_LARGE = "15px"
    PADDING_MEDIUM = "10px"
    PADDING_SMALL = "8px"
    
    # Button sizes
    BUTTON_HEIGHT_LARGE = "45px"
    BUTTON_HEIGHT_MEDIUM = "40px"
    BUTTON_HEIGHT_SMALL = "35px"
    
    # Minimum widths
    BUTTON_MIN_WIDTH = "100px"
    SLIDER_MIN_HEIGHT = "20px"


class ThemeEffects:
    """Visual effects configuration"""
    
    # Shadows
    BOX_SHADOW_LIGHT = "0 2px 5px rgba(0,0,0,0.1)"
    BOX_SHADOW_MEDIUM = "0 2px 8px rgba(52, 152, 219, 0.2)"
    BOX_SHADOW_STRONG = "0 4px 12px rgba(0,0,0,0.15)"
    
    # Transitions
    TRANSITION_FAST = "0.2s ease"
    TRANSITION_MEDIUM = "0.3s ease"
    TRANSITION_SLOW = "0.5s ease"


def get_theme_stylesheet():
    """Generate complete stylesheet from theme configuration"""
    return f"""
    /* Main application styling */
    QMainWindow {{
        background-color: {ThemeColors.PRIMARY_BACKGROUND};
        color: {ThemeColors.PRIMARY_TEXT};
        font-family: {ThemeFonts.PRIMARY_FONT};
    }}
    
    /* Container styling */
    QWidget#videoContainer, QWidget#controlsContainer {{
        background-color: {ThemeColors.CONTAINER_WHITE};
        border: 1px solid {ThemeColors.BORDER_GRAY};
        border-radius: {ThemeMetrics.BORDER_RADIUS_LARGE};
        padding: {ThemeMetrics.PADDING_LARGE};
        margin: {ThemeMetrics.SPACING_SMALL};
    }}
    
    /* Button styling */
    QPushButton {{
        background-color: {ThemeColors.CONTAINER_WHITE};
        color: {ThemeColors.PRIMARY_TEXT};
        border: 2px solid {ThemeColors.BORDER_GRAY};
        border-radius: {ThemeMetrics.BORDER_RADIUS_LARGE};
        padding: {ThemeMetrics.PADDING_SMALL} {ThemeMetrics.SPACING_LARGE};
        font-weight: 600;
        font-size: {ThemeFonts.NORMAL_SIZE};
        min-height: {ThemeMetrics.BUTTON_HEIGHT_MEDIUM};
        min-width: {ThemeMetrics.BUTTON_MIN_WIDTH};
    }}
    
    QPushButton:hover {{
        background-color: {ThemeColors.ACCENT_BLUE};
        color: {ThemeColors.CONTAINER_WHITE};
        border-color: {ThemeColors.ACCENT_BLUE_DARK};
    }}
    """
