"""
Modern SVG Icons for VoiceSubMaster
Professional icon set with consistent styling
"""

from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor
from PySide6.QtCore import Qt, QSize
from PySide6.QtSvg import QSvgRenderer
import io


class IconManager:
    """Manager for creating and caching SVG icons"""
    
    def __init__(self):
        self.icon_cache = {}
    
    def get_icon(self, icon_name, size=24, color="#2C3E50"):
        """Get an icon with specified size and color"""
        cache_key = f"{icon_name}_{size}_{color}"
        
        if cache_key not in self.icon_cache:
            svg_data = self.get_svg_data(icon_name, color)
            if svg_data:
                self.icon_cache[cache_key] = self.create_icon_from_svg(svg_data, size)
            else:
                # Fallback to text icon
                self.icon_cache[cache_key] = self.create_text_icon(icon_name, size, color)
        
        return self.icon_cache[cache_key]
    
    def get_svg_data(self, icon_name, color):
        """Get SVG data for specific icon"""
        icons = {
            "play": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M8 5v14l11-7z"/>
            </svg>''',
            
            "pause": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
            </svg>''',
            
            "stop": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M6 6h12v12H6z"/>
            </svg>''',
            
            "skip_previous": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
            </svg>''',
            
            "skip_next": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
            </svg>''',
            
            "volume_up": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
            </svg>''',
            
            "volume_down": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"/>
            </svg>''',
            
            "volume_off": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
            </svg>''',
            
            "fullscreen": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
            </svg>''',
            
            "settings": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
            </svg>''',
            
            "folder_open": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M20 6h-2l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10z"/>
            </svg>''',
            
            "playlist": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
            </svg>''',
            
            "subtitle": f'''<svg viewBox="0 0 24 24" fill="{color}">
                <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-8 7.5v1c0 .28-.22.5-.5.5H7c-.28 0-.5-.22-.5-.5v-1c0-.28.22-.5.5-.5h4.5c.28 0 .5.22.5.5zm5.5 3.5H16c-.28 0-.5-.22-.5-.5v-1c0-.28.22-.5.5-.5h1.5c.28 0 .5.22.5.5v1c0 .28-.22.5-.5.5zm0-3H16c-.28 0-.5-.22-.5-.5v-1c0-.28.22-.5.5-.5h1.5c.28 0 .5.22.5.5v1c0 .28-.22.5-.5.5z"/>
            </svg>'''
        }
        
        return icons.get(icon_name)
    
    def create_icon_from_svg(self, svg_data, size):
        """Create QIcon from SVG data"""
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        return QIcon(pixmap)
    
    def create_text_icon(self, text, size, color):
        """Create fallback text icon"""
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setPen(QColor(color))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, text[:2])
        painter.end()
        
        return QIcon(pixmap)


# Global icon manager instance
icon_manager = IconManager()
