"""
Main Window for VoiceSubMaster Application
Phase 1: Media Player Interface Foundation
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QStatusBar, QFileDialog, QMessageBox,
    QPushButton, QSlider, QLabel, QFrame
)
from PySide6.QtCore import Qt, QUrl, QTimer, Signal
from PySide6.QtGui import QAction, QKeySequence, QIcon
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
from .styles_pro import PROFESSIONAL_STYLE
from .icons_pro import pro_icon_manager
from .simple_settings import SimpleSettingsDialog
from .welcome_widget import WelcomeWidget


class MainWindow(QMainWindow):
    """Main application window with media player functionality"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VoiceSubMaster - Professional Media Player")
        self.setGeometry(100, 100, 1200, 800)
        
        # Initialize media player components
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        
        # Initialize UI components
        self.video_widget = None
        self.play_button = None
        self.pause_button = None
        self.volume_slider = None
        self.position_slider = None
        self.time_label = None
        self.duration_label = None
        
        # Timer for updating position
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self.update_position)
        self.position_timer.start(1000)  # Update every second
        
        # Setup UI
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_status_bar()
        self.setup_connections()
        self.setup_shortcuts()
        self.apply_styles()
        
        # Current file path
        self.current_file = None
        
    def setup_ui(self):
        """Setup the main user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Video widget
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumHeight(400)
        self.video_widget.setStyleSheet("""
            QVideoWidget {
                background-color: #000000;
                border: 2px solid #333333;
                border-radius: 5px;
            }
        """)
        main_layout.addWidget(self.video_widget)
        
        # Set video output
        self.media_player.setVideoOutput(self.video_widget)
        
        # Controls layout
        controls_layout = self.create_controls_layout()
        main_layout.addLayout(controls_layout)
        
    def create_controls_layout(self):
        """Create media control buttons and sliders"""
        controls_layout = QVBoxLayout()
        
        # Position slider
        position_layout = QHBoxLayout()
        
        self.time_label = QLabel("00:00")
        self.time_label.setMinimumWidth(50)
        position_layout.addWidget(self.time_label)
        
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 0)
        self.position_slider.sliderMoved.connect(self.set_position)
        position_layout.addWidget(self.position_slider)
        
        self.duration_label = QLabel("00:00")
        self.duration_label.setMinimumWidth(50)
        position_layout.addWidget(self.duration_label)
        
        controls_layout.addLayout(position_layout)
        
        # Control buttons layout
        buttons_layout = QHBoxLayout()
        buttons_layout.setAlignment(Qt.AlignCenter)
        
        # Backward 10s button
        self.backward_button = QPushButton()
        self.backward_button.setIcon(pro_icon_manager.get_icon("skip_previous", 32, "#ffffff"))
        self.backward_button.clicked.connect(self.backward_10s)
        self.backward_button.setFixedSize(60, 60)
        self.backward_button.setToolTip("Skip backward 10 seconds (Left Arrow)")
        buttons_layout.addWidget(self.backward_button)

        # Play button
        self.play_button = QPushButton()
        self.play_button.setIcon(pro_icon_manager.get_icon("play", 36, "#ffffff", "green"))
        self.play_button.setObjectName("playButton")
        self.play_button.clicked.connect(self.play_media)
        self.play_button.setFixedSize(70, 70)
        self.play_button.setToolTip("Play media (Spacebar)")
        buttons_layout.addWidget(self.play_button)

        # Pause button
        self.pause_button = QPushButton()
        self.pause_button.setIcon(pro_icon_manager.get_icon("pause", 36, "#ffffff", "red"))
        self.pause_button.setObjectName("pauseButton")
        self.pause_button.clicked.connect(self.pause_media)
        self.pause_button.setFixedSize(70, 70)
        self.pause_button.setEnabled(False)
        self.pause_button.setToolTip("Pause media (Spacebar)")
        buttons_layout.addWidget(self.pause_button)

        # Stop button
        self.stop_button = QPushButton()
        self.stop_button.setIcon(pro_icon_manager.get_icon("stop", 32, "#ffffff"))
        self.stop_button.clicked.connect(self.stop_media)
        self.stop_button.setFixedSize(60, 60)
        self.stop_button.setToolTip("Stop playback")
        buttons_layout.addWidget(self.stop_button)

        # Forward 10s button
        self.forward_button = QPushButton()
        self.forward_button.setIcon(pro_icon_manager.get_icon("skip_next", 32, "#ffffff"))
        self.forward_button.clicked.connect(self.forward_10s)
        self.forward_button.setFixedSize(60, 60)
        self.forward_button.setToolTip("Skip forward 10 seconds (Right Arrow)")
        buttons_layout.addWidget(self.forward_button)

        # Fullscreen button
        self.fullscreen_button = QPushButton()
        self.fullscreen_button.setIcon(pro_icon_manager.get_icon("fullscreen", 28, "#ffffff"))
        self.fullscreen_button.clicked.connect(self.toggle_fullscreen)
        self.fullscreen_button.setFixedSize(60, 60)
        self.fullscreen_button.setToolTip("Toggle fullscreen (F11)")
        buttons_layout.addWidget(self.fullscreen_button)

        # Settings button
        self.settings_button = QPushButton()
        self.settings_button.setIcon(pro_icon_manager.get_icon("settings", 28, "#ffffff", "blue"))
        self.settings_button.clicked.connect(self.open_simple_settings)
        self.settings_button.setFixedSize(60, 60)
        self.settings_button.setToolTip("Open settings")
        buttons_layout.addWidget(self.settings_button)
        
        controls_layout.addLayout(buttons_layout)
        
        # Volume control layout
        volume_layout = QHBoxLayout()
        volume_layout.setAlignment(Qt.AlignCenter)
        
        volume_label = QLabel("🔊 Volume:")
        volume_layout.addWidget(volume_label)
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setMaximumWidth(200)
        self.volume_slider.valueChanged.connect(self.set_volume)
        volume_layout.addWidget(self.volume_slider)
        
        self.volume_label_value = QLabel("70%")
        self.volume_label_value.setMinimumWidth(40)
        volume_layout.addWidget(self.volume_label_value)
        
        controls_layout.addLayout(volume_layout)
        
        return controls_layout

    def setup_menu_bar(self):
        """Setup application menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        # Open file action
        open_action = QAction("&Open File...", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.setStatusTip("Open a media file")
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.setStatusTip("Exit the application")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Help menu
        help_menu = menubar.addMenu("&Help")

        # About action
        about_action = QAction("&About", self)
        about_action.setStatusTip("About VoiceSubMaster")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """Setup application status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("Ready - Open a media file to start")

    def setup_connections(self):
        """Setup signal/slot connections"""
        # Media player connections
        self.media_player.mediaStatusChanged.connect(self.media_status_changed)
        self.media_player.playbackStateChanged.connect(self.playback_state_changed)
        self.media_player.durationChanged.connect(self.duration_changed)
        self.media_player.positionChanged.connect(self.position_changed)
        self.media_player.errorOccurred.connect(self.handle_error)

        # Set initial volume
        self.set_volume(70)

    def setup_shortcuts(self):
        """Setup keyboard shortcuts"""
        # Spacebar for play/pause
        play_pause_shortcut = QAction(self)
        play_pause_shortcut.setShortcut(Qt.Key_Space)
        play_pause_shortcut.triggered.connect(self.toggle_play_pause)
        self.addAction(play_pause_shortcut)

        # Arrow keys for navigation and volume
        left_shortcut = QAction(self)
        left_shortcut.setShortcut(Qt.Key_Left)
        left_shortcut.triggered.connect(self.backward_10s)
        self.addAction(left_shortcut)

        right_shortcut = QAction(self)
        right_shortcut.setShortcut(Qt.Key_Right)
        right_shortcut.triggered.connect(self.forward_10s)
        self.addAction(right_shortcut)

        up_shortcut = QAction(self)
        up_shortcut.setShortcut(Qt.Key_Up)
        up_shortcut.triggered.connect(self.volume_up)
        self.addAction(up_shortcut)

        down_shortcut = QAction(self)
        down_shortcut.setShortcut(Qt.Key_Down)
        down_shortcut.triggered.connect(self.volume_down)
        self.addAction(down_shortcut)

    # Media control methods
    def open_file(self):
        """Open a media file"""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_dialog.setNameFilter(
            "Media Files (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v "
            "*.mp3 *.wav *.flac *.aac *.ogg *.wma *.m4a);;All Files (*)"
        )

        if file_dialog.exec():
            file_path = file_dialog.selectedFiles()[0]
            self.load_media(file_path)

    def load_media(self, file_path):
        """Load media file into player"""
        if not os.path.exists(file_path):
            QMessageBox.warning(self, "Error", f"File not found: {file_path}")
            return

        self.current_file = file_path
        file_url = QUrl.fromLocalFile(file_path)
        self.media_player.setSource(file_url)

        # Update status bar
        file_name = Path(file_path).name
        self.status_bar.showMessage(f"Loaded: {file_name}")

        # Enable controls
        self.play_button.setEnabled(True)
        self.forward_button.setEnabled(True)
        self.backward_button.setEnabled(True)

    def play_media(self):
        """Start media playback"""
        if self.current_file:
            self.media_player.play()

    def pause_media(self):
        """Pause media playback"""
        self.media_player.pause()

    def toggle_play_pause(self):
        """Toggle between play and pause"""
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            self.pause_media()
        else:
            self.play_media()

    def forward_10s(self):
        """Skip forward 10 seconds"""
        current_position = self.media_player.position()
        new_position = min(current_position + 10000, self.media_player.duration())
        self.media_player.setPosition(new_position)

    def backward_10s(self):
        """Skip backward 10 seconds"""
        current_position = self.media_player.position()
        new_position = max(current_position - 10000, 0)
        self.media_player.setPosition(new_position)

    def set_volume(self, value):
        """Set audio volume"""
        volume = value / 100.0
        self.audio_output.setVolume(volume)
        self.volume_label_value.setText(f"{value}%")

    def volume_up(self):
        """Increase volume by 10%"""
        current_volume = self.volume_slider.value()
        new_volume = min(current_volume + 10, 100)
        self.volume_slider.setValue(new_volume)

    def volume_down(self):
        """Decrease volume by 10%"""
        current_volume = self.volume_slider.value()
        new_volume = max(current_volume - 10, 0)
        self.volume_slider.setValue(new_volume)

    def set_position(self, position):
        """Set playback position"""
        self.media_player.setPosition(position)

    # Event handlers
    def media_status_changed(self, status):
        """Handle media status changes"""
        if status == QMediaPlayer.LoadedMedia:
            self.status_bar.showMessage("Media loaded successfully")
        elif status == QMediaPlayer.InvalidMedia:
            self.status_bar.showMessage("Invalid media file")
            QMessageBox.warning(self, "Error", "Cannot load this media file")

    def playback_state_changed(self, state):
        """Handle playback state changes"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.status_bar.showMessage("Playing...")
        else:
            self.play_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            if state == QMediaPlayer.PausedState:
                self.status_bar.showMessage("Paused")
            elif state == QMediaPlayer.StoppedState:
                self.status_bar.showMessage("Stopped")

    def duration_changed(self, duration):
        """Handle duration changes"""
        self.position_slider.setRange(0, duration)
        self.duration_label.setText(self.format_time(duration))

    def position_changed(self, position):
        """Handle position changes"""
        self.position_slider.setValue(position)
        self.time_label.setText(self.format_time(position))

    def update_position(self):
        """Update position display"""
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            position = self.media_player.position()
            self.position_slider.setValue(position)
            self.time_label.setText(self.format_time(position))

    def handle_error(self, error):
        """Handle media player errors"""
        error_messages = {
            QMediaPlayer.NoError: "No error",
            QMediaPlayer.ResourceError: "Resource error - file not found or corrupted",
            QMediaPlayer.FormatError: "Format error - unsupported file format",
            QMediaPlayer.NetworkError: "Network error",
            QMediaPlayer.AccessDeniedError: "Access denied - insufficient permissions"
        }

        error_msg = error_messages.get(error, f"Unknown error: {error}")
        self.status_bar.showMessage(f"Error: {error_msg}")
        QMessageBox.critical(self, "Media Player Error", error_msg)

    # Utility methods
    def format_time(self, milliseconds):
        """Format time in milliseconds to MM:SS format"""
        seconds = milliseconds // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About VoiceSubMaster",
            "<h3>VoiceSubMaster v1.0.0-Phase1</h3>"
            "<p>Professional Desktop Media Player</p>"
            "<p>Phase 1: Media Player Interface Foundation</p>"
            "<p><b>Features:</b></p>"
            "<ul>"
            "<li>Video and audio playback</li>"
            "<li>Keyboard shortcuts</li>"
            "<li>Volume control</li>"
            "<li>Seek functionality</li>"
            "</ul>"
            "<p>Built with PySide6 and Qt</p>"
        )

    def apply_styles(self):
        """Apply professional styles to the application"""
        self.setStyleSheet(PROFESSIONAL_STYLE)



    def stop_media(self):
        """Stop media playback"""
        self.media_player.stop()

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def on_language_changed(self, language):
        """Handle language change"""
        self.status_bar.showMessage(f"Language changed to: {language}")

    def on_mode_changed(self, mode):
        """Handle mode change"""
        self.status_bar.showMessage(f"Mode changed to: {mode}")

    def open_simple_settings(self):
        """Open simple settings dialog"""
        settings_dialog = SimpleSettingsDialog(self)
        settings_dialog.settings_changed.connect(self.apply_new_settings)
        settings_dialog.language_changed.connect(self.on_language_changed)
        settings_dialog.exec()

    def apply_new_settings(self, settings):
        """Apply new settings from dialog"""
        # Apply volume settings
        if "default_volume" in settings:
            self.set_volume(settings["default_volume"])

        # Apply skip duration
        if "skip_duration" in settings:
            self.skip_seconds = settings["skip_duration"]

        self.status_bar.showMessage("Settings applied successfully")

    def stop_media(self):
        """Stop media playback"""
        self.media_player.stop()

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
