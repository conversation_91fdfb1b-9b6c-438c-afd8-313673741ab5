"""
Main Window for VoiceSubMaster Application
Phase 1: Media Player Interface Foundation
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QStatusBar, QFileDialog, QMessageBox,
    QPushButton, QSlider, QLabel, QFrame
)
from PySide6.QtCore import Qt, QUrl, QTimer, Signal
from PySide6.QtGui import QAction, QKeySequence, QIcon
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
from .styles import MAIN_WINDOW_STYLE
from .welcome_widget import WelcomeWidget


class MainWindow(QMainWindow):
    """Main application window with media player functionality"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VoiceSubMaster - Professional Media Player")
        self.setGeometry(100, 100, 1200, 800)
        
        # Initialize media player components
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        
        # Initialize UI components
        self.video_widget = None
        self.play_button = None
        self.pause_button = None
        self.volume_slider = None
        self.position_slider = None
        self.time_label = None
        self.duration_label = None
        
        # Timer for updating position
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self.update_position)
        self.position_timer.start(1000)  # Update every second
        
        # Setup UI
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_status_bar()
        self.setup_connections()
        self.setup_shortcuts()
        self.apply_styles()
        
        # Current file path
        self.current_file = None
        
    def setup_ui(self):
        """Setup the main user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Welcome widget section
        self.welcome_widget = WelcomeWidget()
        self.welcome_widget.language_changed.connect(self.on_language_changed)
        self.welcome_widget.mode_changed.connect(self.on_mode_changed)
        main_layout.addWidget(self.welcome_widget)

        # Video container
        video_container = self.create_video_container()
        main_layout.addWidget(video_container)

        # Controls container
        controls_container = self.create_controls_container()
        main_layout.addWidget(controls_container)

        # Set video output
        self.media_player.setVideoOutput(self.video_widget)
        
    def create_controls_layout(self):
        """Create media control buttons and sliders"""
        controls_layout = QVBoxLayout()
        
        # Position slider
        position_layout = QHBoxLayout()
        
        self.time_label = QLabel("00:00")
        self.time_label.setMinimumWidth(50)
        position_layout.addWidget(self.time_label)
        
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 0)
        self.position_slider.sliderMoved.connect(self.set_position)
        position_layout.addWidget(self.position_slider)
        
        self.duration_label = QLabel("00:00")
        self.duration_label.setMinimumWidth(50)
        position_layout.addWidget(self.duration_label)
        
        controls_layout.addLayout(position_layout)
        
        # Control buttons layout
        buttons_layout = QHBoxLayout()
        buttons_layout.setAlignment(Qt.AlignCenter)
        
        # Backward 10s button
        self.backward_button = QPushButton("⏪ -10s")
        self.backward_button.clicked.connect(self.backward_10s)
        self.backward_button.setMinimumSize(80, 40)
        buttons_layout.addWidget(self.backward_button)
        
        # Play button
        self.play_button = QPushButton("▶️ Play")
        self.play_button.clicked.connect(self.play_media)
        self.play_button.setMinimumSize(100, 40)
        buttons_layout.addWidget(self.play_button)
        
        # Pause button
        self.pause_button = QPushButton("⏸️ Pause")
        self.pause_button.clicked.connect(self.pause_media)
        self.pause_button.setMinimumSize(100, 40)
        self.pause_button.setEnabled(False)
        buttons_layout.addWidget(self.pause_button)
        
        # Forward 10s button
        self.forward_button = QPushButton("⏩ +10s")
        self.forward_button.clicked.connect(self.forward_10s)
        self.forward_button.setMinimumSize(80, 40)
        buttons_layout.addWidget(self.forward_button)
        
        controls_layout.addLayout(buttons_layout)
        
        # Volume control layout
        volume_layout = QHBoxLayout()
        volume_layout.setAlignment(Qt.AlignCenter)
        
        volume_label = QLabel("🔊 Volume:")
        volume_layout.addWidget(volume_label)
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setMaximumWidth(200)
        self.volume_slider.valueChanged.connect(self.set_volume)
        volume_layout.addWidget(self.volume_slider)
        
        self.volume_label_value = QLabel("70%")
        self.volume_label_value.setMinimumWidth(40)
        volume_layout.addWidget(self.volume_label_value)
        
        controls_layout.addLayout(volume_layout)
        
        return controls_layout

    def setup_menu_bar(self):
        """Setup application menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        # Open file action
        open_action = QAction("&Open File...", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.setStatusTip("Open a media file")
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.setStatusTip("Exit the application")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Help menu
        help_menu = menubar.addMenu("&Help")

        # About action
        about_action = QAction("&About", self)
        about_action.setStatusTip("About VoiceSubMaster")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """Setup application status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("Ready - Open a media file to start")

    def setup_connections(self):
        """Setup signal/slot connections"""
        # Media player connections
        self.media_player.mediaStatusChanged.connect(self.media_status_changed)
        self.media_player.playbackStateChanged.connect(self.playback_state_changed)
        self.media_player.durationChanged.connect(self.duration_changed)
        self.media_player.positionChanged.connect(self.position_changed)
        self.media_player.errorOccurred.connect(self.handle_error)

        # Set initial volume
        self.set_volume(70)

    def setup_shortcuts(self):
        """Setup keyboard shortcuts"""
        # Spacebar for play/pause
        play_pause_shortcut = QAction(self)
        play_pause_shortcut.setShortcut(Qt.Key_Space)
        play_pause_shortcut.triggered.connect(self.toggle_play_pause)
        self.addAction(play_pause_shortcut)

        # Arrow keys for navigation and volume
        left_shortcut = QAction(self)
        left_shortcut.setShortcut(Qt.Key_Left)
        left_shortcut.triggered.connect(self.backward_10s)
        self.addAction(left_shortcut)

        right_shortcut = QAction(self)
        right_shortcut.setShortcut(Qt.Key_Right)
        right_shortcut.triggered.connect(self.forward_10s)
        self.addAction(right_shortcut)

        up_shortcut = QAction(self)
        up_shortcut.setShortcut(Qt.Key_Up)
        up_shortcut.triggered.connect(self.volume_up)
        self.addAction(up_shortcut)

        down_shortcut = QAction(self)
        down_shortcut.setShortcut(Qt.Key_Down)
        down_shortcut.triggered.connect(self.volume_down)
        self.addAction(down_shortcut)

    # Media control methods
    def open_file(self):
        """Open a media file"""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_dialog.setNameFilter(
            "Media Files (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v "
            "*.mp3 *.wav *.flac *.aac *.ogg *.wma *.m4a);;All Files (*)"
        )

        if file_dialog.exec():
            file_path = file_dialog.selectedFiles()[0]
            self.load_media(file_path)

    def load_media(self, file_path):
        """Load media file into player"""
        if not os.path.exists(file_path):
            QMessageBox.warning(self, "Error", f"File not found: {file_path}")
            return

        self.current_file = file_path
        file_url = QUrl.fromLocalFile(file_path)
        self.media_player.setSource(file_url)

        # Update status bar
        file_name = Path(file_path).name
        self.status_bar.showMessage(f"Loaded: {file_name}")

        # Enable controls
        self.play_button.setEnabled(True)
        self.forward_button.setEnabled(True)
        self.backward_button.setEnabled(True)

    def play_media(self):
        """Start media playback"""
        if self.current_file:
            self.media_player.play()

    def pause_media(self):
        """Pause media playback"""
        self.media_player.pause()

    def toggle_play_pause(self):
        """Toggle between play and pause"""
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            self.pause_media()
        else:
            self.play_media()

    def forward_10s(self):
        """Skip forward 10 seconds"""
        current_position = self.media_player.position()
        new_position = min(current_position + 10000, self.media_player.duration())
        self.media_player.setPosition(new_position)

    def backward_10s(self):
        """Skip backward 10 seconds"""
        current_position = self.media_player.position()
        new_position = max(current_position - 10000, 0)
        self.media_player.setPosition(new_position)

    def set_volume(self, value):
        """Set audio volume"""
        volume = value / 100.0
        self.audio_output.setVolume(volume)
        self.volume_label_value.setText(f"{value}%")

    def volume_up(self):
        """Increase volume by 10%"""
        current_volume = self.volume_slider.value()
        new_volume = min(current_volume + 10, 100)
        self.volume_slider.setValue(new_volume)

    def volume_down(self):
        """Decrease volume by 10%"""
        current_volume = self.volume_slider.value()
        new_volume = max(current_volume - 10, 0)
        self.volume_slider.setValue(new_volume)

    def set_position(self, position):
        """Set playback position"""
        self.media_player.setPosition(position)

    # Event handlers
    def media_status_changed(self, status):
        """Handle media status changes"""
        if status == QMediaPlayer.LoadedMedia:
            self.status_bar.showMessage("Media loaded successfully")
        elif status == QMediaPlayer.InvalidMedia:
            self.status_bar.showMessage("Invalid media file")
            QMessageBox.warning(self, "Error", "Cannot load this media file")

    def playback_state_changed(self, state):
        """Handle playback state changes"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.status_bar.showMessage("Playing...")
        else:
            self.play_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            if state == QMediaPlayer.PausedState:
                self.status_bar.showMessage("Paused")
            elif state == QMediaPlayer.StoppedState:
                self.status_bar.showMessage("Stopped")

    def duration_changed(self, duration):
        """Handle duration changes"""
        self.position_slider.setRange(0, duration)
        self.duration_label.setText(self.format_time(duration))

    def position_changed(self, position):
        """Handle position changes"""
        self.position_slider.setValue(position)
        self.time_label.setText(self.format_time(position))

    def update_position(self):
        """Update position display"""
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            position = self.media_player.position()
            self.position_slider.setValue(position)
            self.time_label.setText(self.format_time(position))

    def handle_error(self, error):
        """Handle media player errors"""
        error_messages = {
            QMediaPlayer.NoError: "No error",
            QMediaPlayer.ResourceError: "Resource error - file not found or corrupted",
            QMediaPlayer.FormatError: "Format error - unsupported file format",
            QMediaPlayer.NetworkError: "Network error",
            QMediaPlayer.AccessDeniedError: "Access denied - insufficient permissions"
        }

        error_msg = error_messages.get(error, f"Unknown error: {error}")
        self.status_bar.showMessage(f"Error: {error_msg}")
        QMessageBox.critical(self, "Media Player Error", error_msg)

    # Utility methods
    def format_time(self, milliseconds):
        """Format time in milliseconds to MM:SS format"""
        seconds = milliseconds // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About VoiceSubMaster",
            "<h3>VoiceSubMaster v1.0.0-Phase1</h3>"
            "<p>Professional Desktop Media Player</p>"
            "<p>Phase 1: Media Player Interface Foundation</p>"
            "<p><b>Features:</b></p>"
            "<ul>"
            "<li>Video and audio playback</li>"
            "<li>Keyboard shortcuts</li>"
            "<li>Volume control</li>"
            "<li>Seek functionality</li>"
            "</ul>"
            "<p>Built with PySide6 and Qt</p>"
        )

    def apply_styles(self):
        """Apply custom styles to the application"""
        self.setStyleSheet(MAIN_WINDOW_STYLE)

    # Event handlers for welcome widget
    def on_language_changed(self, language):
        """Handle language change"""
        self.status_bar.showMessage(f"Language changed to: {language}")
        # TODO: Implement language switching in future phases

    def on_mode_changed(self, mode):
        """Handle mode change"""
        self.status_bar.showMessage(f"Mode changed to: {mode}")
        # TODO: Implement mode switching in future phases
