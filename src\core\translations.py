"""
Multi-language Translation System for VoiceSubMaster
Complete translation support for 8 languages
"""

import json
import os
from typing import Dict, Any

class TranslationManager:
    """Manages translations for multiple languages"""
    
    def __init__(self):
        self.current_language = "en"
        self.translations = {}
        self.load_all_translations()
    
    def load_all_translations(self):
        """Load all translation files"""
        # English (Default)
        self.translations["en"] = {
            # Window titles
            "app_title": "VoiceSubMaster - Professional Media Player",
            "settings_title": "Settings",
            "about_title": "About VoiceSubMaster",
            
            # Menu items
            "menu_file": "&File",
            "menu_open": "&Open File",
            "menu_recent": "&Recent Files",
            "menu_exit": "E&xit",
            "menu_view": "&View",
            "menu_fullscreen": "&Fullscreen",
            "menu_settings": "&Settings",
            "menu_help": "&Help",
            "menu_about": "&About",
            
            # Control buttons tooltips
            "tooltip_backward": "Skip backward 10 seconds (<PERSON> Arrow)",
            "tooltip_play": "Play media (Spacebar)",
            "tooltip_pause": "Pause media (Spacebar)",
            "tooltip_forward": "Skip forward 10 seconds (Right Arrow)",
            "tooltip_stop": "Stop playback",
            "tooltip_fullscreen": "Toggle fullscreen (F11)",
            "tooltip_settings": "Open settings",
            "tooltip_volume": "Volume control",
            
            # Status messages
            "status_ready": "Ready - Open a media file to start",
            "status_playing": "Playing...",
            "status_paused": "Paused",
            "status_stopped": "Stopped",
            "status_loading": "Loading file...",
            "status_error": "Error loading file",
            
            # Settings dialog
            "settings_language": "Language Settings",
            "settings_interface_lang": "Interface Language:",
            "settings_playback": "Playback Settings",
            "settings_skip_duration": "Skip Duration:",
            "settings_auto_play": "Auto-play when file opens",
            "settings_remember_position": "Remember playback position",
            "settings_audio": "Audio Settings",
            "settings_default_volume": "Default Volume:",
            "settings_volume_boost": "Enable volume boost (up to 200%)",
            "settings_appearance": "Appearance",
            "settings_theme": "Theme:",
            "settings_theme_professional": "Professional",
            "settings_theme_dark": "Dark Mode",
            "settings_theme_light": "Light Mode",
            
            # Buttons
            "button_reset": "Reset",
            "button_cancel": "Cancel",
            "button_apply": "Apply",
            "button_ok": "OK",
            "button_open": "Open",
            "button_close": "Close",
            
            # File dialog
            "dialog_open_file": "Open Media File",
            "dialog_media_files": "Media Files",
            "dialog_all_files": "All Files",
            
            # About dialog
            "about_version": "Version 1.0.0 - Phase 1",
            "about_description": "Professional media player with advanced features",
            "about_features": "Features:",
            "about_feature_video": "Video and audio playback",
            "about_feature_shortcuts": "Keyboard shortcuts",
            "about_feature_volume": "Volume control",
            "about_feature_seek": "Seek functionality",
            "about_built_with": "Built with PySide6 and Qt",
            
            # Time format
            "time_format": "{minutes}:{seconds:02d}",
            "duration_unknown": "--:--"
        }
        
        # Arabic
        self.translations["ar"] = {
            "app_title": "VoiceSubMaster - مشغل الوسائط الاحترافي",
            "settings_title": "الإعدادات",
            "about_title": "حول VoiceSubMaster",
            
            "menu_file": "&ملف",
            "menu_open": "&فتح ملف",
            "menu_recent": "&الملفات الأخيرة",
            "menu_exit": "&خروج",
            "menu_view": "&عرض",
            "menu_fullscreen": "&ملء الشاشة",
            "menu_settings": "&الإعدادات",
            "menu_help": "&مساعدة",
            "menu_about": "&حول",
            
            "tooltip_backward": "التراجع 10 ثوانٍ (السهم الأيسر)",
            "tooltip_play": "تشغيل الوسائط (مسطرة المسافة)",
            "tooltip_pause": "إيقاف مؤقت (مسطرة المسافة)",
            "tooltip_forward": "التقدم 10 ثوانٍ (السهم الأيمن)",
            "tooltip_stop": "إيقاف التشغيل",
            "tooltip_fullscreen": "تبديل ملء الشاشة (F11)",
            "tooltip_settings": "فتح الإعدادات",
            "tooltip_volume": "التحكم في الصوت",
            
            "status_ready": "جاهز - افتح ملف وسائط للبدء",
            "status_playing": "يتم التشغيل...",
            "status_paused": "متوقف مؤقتاً",
            "status_stopped": "متوقف",
            "status_loading": "تحميل الملف...",
            "status_error": "خطأ في تحميل الملف",
            
            "settings_language": "إعدادات اللغة",
            "settings_interface_lang": "لغة الواجهة:",
            "settings_playback": "إعدادات التشغيل",
            "settings_skip_duration": "مدة التخطي:",
            "settings_auto_play": "التشغيل التلقائي عند فتح الملف",
            "settings_remember_position": "تذكر موضع التشغيل",
            "settings_audio": "إعدادات الصوت",
            "settings_default_volume": "الصوت الافتراضي:",
            "settings_volume_boost": "تمكين تعزيز الصوت (حتى 200%)",
            "settings_appearance": "المظهر",
            "settings_theme": "السمة:",
            "settings_theme_professional": "احترافي",
            "settings_theme_dark": "الوضع المظلم",
            "settings_theme_light": "الوضع المضيء",
            
            "button_reset": "إعادة تعيين",
            "button_cancel": "إلغاء",
            "button_apply": "تطبيق",
            "button_ok": "موافق",
            "button_open": "فتح",
            "button_close": "إغلاق",
            
            "dialog_open_file": "فتح ملف وسائط",
            "dialog_media_files": "ملفات الوسائط",
            "dialog_all_files": "جميع الملفات",
            
            "about_version": "الإصدار 1.0.0 - المرحلة الأولى",
            "about_description": "مشغل وسائط احترافي مع ميزات متقدمة",
            "about_features": "الميزات:",
            "about_feature_video": "تشغيل الفيديو والصوت",
            "about_feature_shortcuts": "اختصارات لوحة المفاتيح",
            "about_feature_volume": "التحكم في الصوت",
            "about_feature_seek": "وظيفة البحث",
            "about_built_with": "مبني باستخدام PySide6 و Qt",
            
            "time_format": "{minutes}:{seconds:02d}",
            "duration_unknown": "--:--"
        }
        
        # French
        self.translations["fr"] = {
            "app_title": "VoiceSubMaster - Lecteur Multimédia Professionnel",
            "settings_title": "Paramètres",
            "about_title": "À propos de VoiceSubMaster",
            
            "menu_file": "&Fichier",
            "menu_open": "&Ouvrir un fichier",
            "menu_recent": "Fichiers &récents",
            "menu_exit": "&Quitter",
            "menu_view": "&Affichage",
            "menu_fullscreen": "&Plein écran",
            "menu_settings": "&Paramètres",
            "menu_help": "&Aide",
            "menu_about": "&À propos",
            
            "tooltip_backward": "Reculer de 10 secondes (Flèche gauche)",
            "tooltip_play": "Lire le média (Barre d'espace)",
            "tooltip_pause": "Mettre en pause (Barre d'espace)",
            "tooltip_forward": "Avancer de 10 secondes (Flèche droite)",
            "tooltip_stop": "Arrêter la lecture",
            "tooltip_fullscreen": "Basculer en plein écran (F11)",
            "tooltip_settings": "Ouvrir les paramètres",
            "tooltip_volume": "Contrôle du volume",
            
            "status_ready": "Prêt - Ouvrez un fichier multimédia pour commencer",
            "status_playing": "Lecture en cours...",
            "status_paused": "En pause",
            "status_stopped": "Arrêté",
            "status_loading": "Chargement du fichier...",
            "status_error": "Erreur lors du chargement du fichier",
            
            "settings_language": "Paramètres de langue",
            "settings_interface_lang": "Langue de l'interface:",
            "settings_playback": "Paramètres de lecture",
            "settings_skip_duration": "Durée de saut:",
            "settings_auto_play": "Lecture automatique à l'ouverture du fichier",
            "settings_remember_position": "Se souvenir de la position de lecture",
            "settings_audio": "Paramètres audio",
            "settings_default_volume": "Volume par défaut:",
            "settings_volume_boost": "Activer l'amplification du volume (jusqu'à 200%)",
            "settings_appearance": "Apparence",
            "settings_theme": "Thème:",
            "settings_theme_professional": "Professionnel",
            "settings_theme_dark": "Mode sombre",
            "settings_theme_light": "Mode clair",
            
            "button_reset": "Réinitialiser",
            "button_cancel": "Annuler",
            "button_apply": "Appliquer",
            "button_ok": "OK",
            "button_open": "Ouvrir",
            "button_close": "Fermer",
            
            "dialog_open_file": "Ouvrir un fichier multimédia",
            "dialog_media_files": "Fichiers multimédia",
            "dialog_all_files": "Tous les fichiers",
            
            "about_version": "Version 1.0.0 - Phase 1",
            "about_description": "Lecteur multimédia professionnel avec fonctionnalités avancées",
            "about_features": "Fonctionnalités:",
            "about_feature_video": "Lecture vidéo et audio",
            "about_feature_shortcuts": "Raccourcis clavier",
            "about_feature_volume": "Contrôle du volume",
            "about_feature_seek": "Fonctionnalité de recherche",
            "about_built_with": "Construit avec PySide6 et Qt",
            
            "time_format": "{minutes}:{seconds:02d}",
            "duration_unknown": "--:--"
        }
        
        # Spanish
        self.translations["es"] = {
            "app_title": "VoiceSubMaster - Reproductor Multimedia Profesional",
            "settings_title": "Configuración",
            "about_title": "Acerca de VoiceSubMaster",
            
            "menu_file": "&Archivo",
            "menu_open": "&Abrir archivo",
            "menu_recent": "Archivos &recientes",
            "menu_exit": "&Salir",
            "menu_view": "&Ver",
            "menu_fullscreen": "&Pantalla completa",
            "menu_settings": "&Configuración",
            "menu_help": "&Ayuda",
            "menu_about": "&Acerca de",
            
            "tooltip_backward": "Retroceder 10 segundos (Flecha izquierda)",
            "tooltip_play": "Reproducir multimedia (Barra espaciadora)",
            "tooltip_pause": "Pausar multimedia (Barra espaciadora)",
            "tooltip_forward": "Avanzar 10 segundos (Flecha derecha)",
            "tooltip_stop": "Detener reproducción",
            "tooltip_fullscreen": "Alternar pantalla completa (F11)",
            "tooltip_settings": "Abrir configuración",
            "tooltip_volume": "Control de volumen",
            
            "status_ready": "Listo - Abra un archivo multimedia para comenzar",
            "status_playing": "Reproduciendo...",
            "status_paused": "Pausado",
            "status_stopped": "Detenido",
            "status_loading": "Cargando archivo...",
            "status_error": "Error al cargar el archivo",
            
            "settings_language": "Configuración de idioma",
            "settings_interface_lang": "Idioma de la interfaz:",
            "settings_playback": "Configuración de reproducción",
            "settings_skip_duration": "Duración del salto:",
            "settings_auto_play": "Reproducción automática al abrir archivo",
            "settings_remember_position": "Recordar posición de reproducción",
            "settings_audio": "Configuración de audio",
            "settings_default_volume": "Volumen predeterminado:",
            "settings_volume_boost": "Habilitar amplificación de volumen (hasta 200%)",
            "settings_appearance": "Apariencia",
            "settings_theme": "Tema:",
            "settings_theme_professional": "Profesional",
            "settings_theme_dark": "Modo oscuro",
            "settings_theme_light": "Modo claro",
            
            "button_reset": "Restablecer",
            "button_cancel": "Cancelar",
            "button_apply": "Aplicar",
            "button_ok": "Aceptar",
            "button_open": "Abrir",
            "button_close": "Cerrar",
            
            "dialog_open_file": "Abrir archivo multimedia",
            "dialog_media_files": "Archivos multimedia",
            "dialog_all_files": "Todos los archivos",
            
            "about_version": "Versión 1.0.0 - Fase 1",
            "about_description": "Reproductor multimedia profesional con características avanzadas",
            "about_features": "Características:",
            "about_feature_video": "Reproducción de video y audio",
            "about_feature_shortcuts": "Atajos de teclado",
            "about_feature_volume": "Control de volumen",
            "about_feature_seek": "Funcionalidad de búsqueda",
            "about_built_with": "Construido con PySide6 y Qt",
            
            "time_format": "{minutes}:{seconds:02d}",
            "duration_unknown": "--:--"
        }
    
    def set_language(self, language_code: str):
        """Set the current language"""
        if language_code in self.translations:
            self.current_language = language_code
            return True
        return False
    
    def get_text(self, key: str, **kwargs) -> str:
        """Get translated text for the current language"""
        if self.current_language in self.translations:
            text = self.translations[self.current_language].get(key, key)
        else:
            text = self.translations["en"].get(key, key)
        
        # Format text with provided arguments
        if kwargs:
            try:
                text = text.format(**kwargs)
            except (KeyError, ValueError):
                pass
        
        return text
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get list of available languages"""
        return {
            "en": "🇺🇸 English",
            "ar": "🇸🇦 العربية",
            "fr": "🇫🇷 Français", 
            "es": "🇪🇸 Español"
        }
    
    def save_language_preference(self, language_code: str):
        """Save language preference to settings"""
        settings_dir = "config"
        settings_file = os.path.join(settings_dir, "language.json")
        
        os.makedirs(settings_dir, exist_ok=True)
        
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump({"language": language_code}, f, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving language preference: {e}")
    
    def load_language_preference(self) -> str:
        """Load language preference from settings"""
        settings_file = os.path.join("config", "language.json")
        
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get("language", "en")
        except Exception as e:
            print(f"Error loading language preference: {e}")
        
        return "en"

# Global translation manager instance
translation_manager = TranslationManager()
