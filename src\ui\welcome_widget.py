"""
Welcome widget with language selection buttons
Inspired by the reference design
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont


class WelcomeWidget(QWidget):
    """Welcome widget with language selection and mode buttons"""
    
    language_changed = Signal(str)  # Signal for language change
    mode_changed = Signal(str)      # Signal for mode change
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """Setup the welcome widget UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Welcome text section
        welcome_layout = self.create_welcome_section()
        layout.addLayout(welcome_layout)
        
        # Language buttons section
        language_layout = self.create_language_section()
        layout.addLayout(language_layout)
        
        # Mode buttons section
        mode_layout = self.create_mode_section()
        layout.addLayout(mode_layout)
        
        layout.addStretch()
    
    def create_welcome_section(self):
        """Create welcome text section"""
        welcome_layout = QHBoxLayout()
        
        # English welcome
        self.english_label = QLabel("Hello, how can I help you today?")
        self.english_label.setAlignment(Qt.AlignLeft)
        self.english_label.setObjectName("englishWelcome")
        
        # Arabic welcome
        self.arabic_label = QLabel("مرحباً، كيف يمكن مساعدتك اليوم؟")
        self.arabic_label.setAlignment(Qt.AlignRight)
        self.arabic_label.setObjectName("arabicWelcome")
        
        welcome_layout.addWidget(self.english_label)
        welcome_layout.addStretch()
        welcome_layout.addWidget(self.arabic_label)
        
        return welcome_layout
    
    def create_language_section(self):
        """Create language selection buttons"""
        language_layout = QHBoxLayout()
        language_layout.setAlignment(Qt.AlignCenter)
        language_layout.setSpacing(15)
        
        # English button
        self.english_btn = QPushButton("English")
        self.english_btn.setObjectName("englishButton")
        self.english_btn.clicked.connect(lambda: self.language_changed.emit("en"))
        
        # Arabic button
        self.arabic_btn = QPushButton("العربية")
        self.arabic_btn.setObjectName("arabicButton")
        self.arabic_btn.clicked.connect(lambda: self.language_changed.emit("ar"))
        
        language_layout.addWidget(self.english_btn)
        language_layout.addWidget(self.arabic_btn)
        
        return language_layout
    
    def create_mode_section(self):
        """Create mode selection buttons"""
        mode_layout = QHBoxLayout()
        mode_layout.setAlignment(Qt.AlignCenter)
        mode_layout.setSpacing(15)
        
        # Text mode button
        self.text_btn = QPushButton("📝 Text")
        self.text_btn.setObjectName("textButton")
        self.text_btn.clicked.connect(lambda: self.mode_changed.emit("text"))
        
        # Voice mode button
        self.voice_btn = QPushButton("🎤 Voice")
        self.voice_btn.setObjectName("voiceButton")
        self.voice_btn.clicked.connect(lambda: self.mode_changed.emit("voice"))
        
        mode_layout.addWidget(self.text_btn)
        mode_layout.addWidget(self.voice_btn)
        
        return mode_layout
    
    def apply_styles(self):
        """Apply custom styles"""
        self.setStyleSheet("""
            QLabel#englishWelcome {
                color: #2C3E50;
                font-family: 'Inter', 'Segoe UI', sans-serif;
                font-size: 16px;
                font-weight: 500;
            }
            
            QLabel#arabicWelcome {
                color: #2C3E50;
                font-family: 'Amiri', 'Traditional Arabic', serif;
                font-size: 16px;
                font-weight: 500;
            }
            
            QPushButton#englishButton {
                background-color: #3498DB;
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 600;
                min-width: 100px;
            }
            
            QPushButton#englishButton:hover {
                background-color: #2980B9;
            }
            
            QPushButton#arabicButton {
                background-color: #E74C3C;
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 600;
                min-width: 100px;
            }
            
            QPushButton#arabicButton:hover {
                background-color: #C0392B;
            }
            
            QPushButton#textButton, QPushButton#voiceButton {
                background-color: #FFFFFF;
                color: #2C3E50;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 500;
                min-width: 100px;
            }
            
            QPushButton#textButton:hover, QPushButton#voiceButton:hover {
                border-color: #3498DB;
                background-color: #EBF5FB;
            }
        """)
