"""
Advanced Settings Dialog for VoiceSubMaster
Professional settings interface with multiple tabs
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QSlider, QSpinBox, QComboBox, QCheckBox, QPushButton,
    QGroupBox, QFormLayout, QColorDialog, QFontDialog, QFileDialog,
    QLineEdit, QTextEdit, QScrollArea, QFrame
)
from PySide6.QtCore import Qt, Signal, QSettings
from PySide6.QtGui import QFont, QColor
import json
import os


class SettingsDialog(QDialog):
    """Advanced settings dialog with multiple categories"""
    
    settings_changed = Signal(dict)  # Signal when settings change
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("VoiceSubMaster Settings")
        self.setModal(True)
        self.resize(600, 500)
        
        # Load current settings
        self.settings = self.load_settings()
        
        self.setup_ui()
        self.load_current_settings()
        self.apply_styles()
    
    def setup_ui(self):
        """Setup the settings dialog UI"""
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_general_tab()
        self.create_playback_tab()
        self.create_audio_tab()
        self.create_interface_tab()
        self.create_advanced_tab()
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.reset_button = QPushButton("Reset to Defaults")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        self.apply_button = QPushButton("Apply")
        self.apply_button.clicked.connect(self.apply_settings)
        button_layout.addWidget(self.apply_button)
        
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept_settings)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
    
    def create_general_tab(self):
        """Create general settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Language settings
        lang_group = QGroupBox("Language Settings")
        lang_layout = QFormLayout(lang_group)
        
        self.interface_lang = QComboBox()
        self.interface_lang.addItems(["English", "العربية", "Français"])
        lang_layout.addRow("Interface Language:", self.interface_lang)
        
        self.default_subtitle_lang = QComboBox()
        self.default_subtitle_lang.addItems(["Auto", "English", "Arabic", "French"])
        lang_layout.addRow("Default Subtitle Language:", self.default_subtitle_lang)
        
        layout.addWidget(lang_group)
        
        # File associations
        file_group = QGroupBox("File Associations")
        file_layout = QVBoxLayout(file_group)
        
        self.auto_load_subtitles = QCheckBox("Automatically load subtitle files")
        file_layout.addWidget(self.auto_load_subtitles)
        
        self.remember_position = QCheckBox("Remember playback position")
        file_layout.addWidget(self.remember_position)
        
        self.auto_play = QCheckBox("Auto-play when file is opened")
        file_layout.addWidget(self.auto_play)
        
        layout.addWidget(file_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "General")
    
    def create_playback_tab(self):
        """Create playback settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Playback controls
        playback_group = QGroupBox("Playback Controls")
        playback_layout = QFormLayout(playback_group)
        
        self.skip_duration = QSpinBox()
        self.skip_duration.setRange(1, 60)
        self.skip_duration.setSuffix(" seconds")
        playback_layout.addRow("Skip Duration:", self.skip_duration)
        
        self.playback_speed = QComboBox()
        self.playback_speed.addItems(["0.25x", "0.5x", "0.75x", "1.0x", "1.25x", "1.5x", "2.0x"])
        playback_layout.addRow("Default Speed:", self.playback_speed)
        
        layout.addWidget(playback_group)
        
        # Video settings
        video_group = QGroupBox("Video Settings")
        video_layout = QFormLayout(video_group)
        
        self.aspect_ratio = QComboBox()
        self.aspect_ratio.addItems(["Auto", "16:9", "4:3", "21:9", "1:1"])
        video_layout.addRow("Aspect Ratio:", self.aspect_ratio)
        
        self.video_quality = QComboBox()
        self.video_quality.addItems(["Auto", "720p", "1080p", "4K"])
        video_layout.addRow("Preferred Quality:", self.video_quality)
        
        layout.addWidget(video_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Playback")
    
    def create_audio_tab(self):
        """Create audio settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Audio output
        audio_group = QGroupBox("Audio Output")
        audio_layout = QFormLayout(audio_group)
        
        self.audio_device = QComboBox()
        self.audio_device.addItems(["Default", "Speakers", "Headphones"])
        audio_layout.addRow("Output Device:", self.audio_device)
        
        self.audio_channels = QComboBox()
        self.audio_channels.addItems(["Auto", "Stereo", "5.1", "7.1"])
        audio_layout.addRow("Audio Channels:", self.audio_channels)
        
        layout.addWidget(audio_group)
        
        # Volume settings
        volume_group = QGroupBox("Volume Settings")
        volume_layout = QFormLayout(volume_group)
        
        self.default_volume = QSlider(Qt.Horizontal)
        self.default_volume.setRange(0, 100)
        self.volume_label = QLabel("70%")
        self.default_volume.valueChanged.connect(
            lambda v: self.volume_label.setText(f"{v}%")
        )
        
        volume_h_layout = QHBoxLayout()
        volume_h_layout.addWidget(self.default_volume)
        volume_h_layout.addWidget(self.volume_label)
        volume_layout.addRow("Default Volume:", volume_h_layout)
        
        self.volume_boost = QCheckBox("Enable volume boost (up to 200%)")
        volume_layout.addRow("", self.volume_boost)
        
        layout.addWidget(volume_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Audio")
    
    def create_interface_tab(self):
        """Create interface settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Theme settings
        theme_group = QGroupBox("Theme & Appearance")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_style = QComboBox()
        self.theme_style.addItems(["Light", "Dark", "Auto"])
        theme_layout.addRow("Theme:", self.theme_style)
        
        self.accent_color = QPushButton("Choose Color")
        self.accent_color.clicked.connect(self.choose_accent_color)
        theme_layout.addRow("Accent Color:", self.accent_color)
        
        layout.addWidget(theme_group)
        
        # Window settings
        window_group = QGroupBox("Window Settings")
        window_layout = QFormLayout(window_group)
        
        self.always_on_top = QCheckBox("Always on top")
        window_layout.addRow("", self.always_on_top)
        
        self.minimize_to_tray = QCheckBox("Minimize to system tray")
        window_layout.addRow("", self.minimize_to_tray)
        
        self.fullscreen_controls = QCheckBox("Show controls in fullscreen")
        window_layout.addRow("", self.fullscreen_controls)
        
        layout.addWidget(window_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Interface")
    
    def create_advanced_tab(self):
        """Create advanced settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Performance settings
        perf_group = QGroupBox("Performance")
        perf_layout = QFormLayout(perf_group)
        
        self.hardware_acceleration = QCheckBox("Enable hardware acceleration")
        perf_layout.addRow("", self.hardware_acceleration)
        
        self.cache_size = QSpinBox()
        self.cache_size.setRange(64, 2048)
        self.cache_size.setSuffix(" MB")
        perf_layout.addRow("Cache Size:", self.cache_size)
        
        layout.addWidget(perf_group)
        
        # Debug settings
        debug_group = QGroupBox("Debug & Logging")
        debug_layout = QFormLayout(debug_group)
        
        self.enable_logging = QCheckBox("Enable debug logging")
        debug_layout.addRow("", self.enable_logging)
        
        self.log_level = QComboBox()
        self.log_level.addItems(["ERROR", "WARNING", "INFO", "DEBUG"])
        debug_layout.addRow("Log Level:", self.log_level)
        
        layout.addWidget(debug_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Advanced")
    
    def choose_accent_color(self):
        """Open color picker for accent color"""
        color = QColorDialog.getColor(QColor("#3498DB"), self)
        if color.isValid():
            self.accent_color.setStyleSheet(f"background-color: {color.name()}")
    
    def load_settings(self):
        """Load settings from file"""
        settings_file = "config/user_settings.json"
        default_settings = {
            "interface_language": "English",
            "subtitle_language": "Auto",
            "auto_load_subtitles": True,
            "remember_position": True,
            "auto_play": False,
            "skip_duration": 10,
            "playback_speed": "1.0x",
            "aspect_ratio": "Auto",
            "video_quality": "Auto",
            "audio_device": "Default",
            "audio_channels": "Auto",
            "default_volume": 70,
            "volume_boost": False,
            "theme_style": "Light",
            "accent_color": "#3498DB",
            "always_on_top": False,
            "minimize_to_tray": False,
            "fullscreen_controls": True,
            "hardware_acceleration": True,
            "cache_size": 256,
            "enable_logging": False,
            "log_level": "WARNING"
        }
        
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    default_settings.update(loaded_settings)
        except Exception as e:
            print(f"Error loading settings: {e}")
        
        return default_settings
    
    def load_current_settings(self):
        """Load current settings into UI"""
        # General tab
        self.interface_lang.setCurrentText(self.settings["interface_language"])
        self.default_subtitle_lang.setCurrentText(self.settings["subtitle_language"])
        self.auto_load_subtitles.setChecked(self.settings["auto_load_subtitles"])
        self.remember_position.setChecked(self.settings["remember_position"])
        self.auto_play.setChecked(self.settings["auto_play"])
        
        # Playback tab
        self.skip_duration.setValue(self.settings["skip_duration"])
        self.playback_speed.setCurrentText(self.settings["playback_speed"])
        self.aspect_ratio.setCurrentText(self.settings["aspect_ratio"])
        self.video_quality.setCurrentText(self.settings["video_quality"])
        
        # Audio tab
        self.audio_device.setCurrentText(self.settings["audio_device"])
        self.audio_channels.setCurrentText(self.settings["audio_channels"])
        self.default_volume.setValue(self.settings["default_volume"])
        self.volume_boost.setChecked(self.settings["volume_boost"])
        
        # Interface tab
        self.theme_style.setCurrentText(self.settings["theme_style"])
        self.accent_color.setStyleSheet(f"background-color: {self.settings['accent_color']}")
        self.always_on_top.setChecked(self.settings["always_on_top"])
        self.minimize_to_tray.setChecked(self.settings["minimize_to_tray"])
        self.fullscreen_controls.setChecked(self.settings["fullscreen_controls"])
        
        # Advanced tab
        self.hardware_acceleration.setChecked(self.settings["hardware_acceleration"])
        self.cache_size.setValue(self.settings["cache_size"])
        self.enable_logging.setChecked(self.settings["enable_logging"])
        self.log_level.setCurrentText(self.settings["log_level"])
    
    def save_settings(self):
        """Save current settings to file"""
        # Update settings from UI
        self.settings.update({
            "interface_language": self.interface_lang.currentText(),
            "subtitle_language": self.default_subtitle_lang.currentText(),
            "auto_load_subtitles": self.auto_load_subtitles.isChecked(),
            "remember_position": self.remember_position.isChecked(),
            "auto_play": self.auto_play.isChecked(),
            "skip_duration": self.skip_duration.value(),
            "playback_speed": self.playback_speed.currentText(),
            "aspect_ratio": self.aspect_ratio.currentText(),
            "video_quality": self.video_quality.currentText(),
            "audio_device": self.audio_device.currentText(),
            "audio_channels": self.audio_channels.currentText(),
            "default_volume": self.default_volume.value(),
            "volume_boost": self.volume_boost.isChecked(),
            "theme_style": self.theme_style.currentText(),
            "always_on_top": self.always_on_top.isChecked(),
            "minimize_to_tray": self.minimize_to_tray.isChecked(),
            "fullscreen_controls": self.fullscreen_controls.isChecked(),
            "hardware_acceleration": self.hardware_acceleration.isChecked(),
            "cache_size": self.cache_size.value(),
            "enable_logging": self.enable_logging.isChecked(),
            "log_level": self.log_level.currentText()
        })
        
        # Save to file
        settings_file = "config/user_settings.json"
        os.makedirs(os.path.dirname(settings_file), exist_ok=True)
        
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving settings: {e}")
    
    def apply_settings(self):
        """Apply settings without closing dialog"""
        self.save_settings()
        self.settings_changed.emit(self.settings)
    
    def accept_settings(self):
        """Accept and apply settings"""
        self.apply_settings()
        self.accept()
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        # Reset to default values
        self.settings = self.load_settings()  # This loads defaults
        self.load_current_settings()
    
    def apply_styles(self):
        """Apply custom styles to the dialog"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ECF0F1;
                color: #2C3E50;
            }
            
            QTabWidget::pane {
                border: 1px solid #BDC3C7;
                border-radius: 8px;
                background-color: #FFFFFF;
            }
            
            QTabBar::tab {
                background-color: #F8F9F9;
                color: #2C3E50;
                padding: 8px 16px;
                margin: 2px;
                border-radius: 4px;
            }
            
            QTabBar::tab:selected {
                background-color: #3498DB;
                color: #FFFFFF;
            }
            
            QGroupBox {
                font-weight: 600;
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                margin: 10px 0;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
