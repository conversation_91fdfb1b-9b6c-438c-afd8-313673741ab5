"""
Settings Dialog for VoiceSubMaster with Multi-language Support
Complete translation support and language switching
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QGroupBox, QFormLayout, QCheckBox, QSlider,
    QSpinBox, QButtonGroup, QRadioButton, QScrollArea, QWidget
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
import json
import os
import sys

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from core.translations import translation_manager


class SettingsDialog(QDialog):
    """Settings dialog with full translation support"""
    
    settings_changed = Signal(dict)
    language_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translation_manager = translation_manager
        
        # Load current language preference
        saved_lang = self.translation_manager.load_language_preference()
        self.translation_manager.set_language(saved_lang)
        
        self.setModal(True)
        self.resize(600, 500)
        self.setMinimumSize(550, 450)
        
        # Load current settings
        self.settings = self.load_settings()
        
        self.setup_ui()
        self.load_current_settings()
        self.apply_styles()
        self.update_ui_language()
    
    def setup_ui(self):
        """Setup the settings UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Create scroll area for better organization
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(20)
        
        # Title
        self.title_label = QLabel()
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Arial", 18, QFont.Bold))
        scroll_layout.addWidget(self.title_label)
        
        # Language Settings Group
        self.lang_group = QGroupBox()
        lang_layout = QFormLayout(self.lang_group)
        
        # Interface Language
        self.interface_lang_label = QLabel()
        self.interface_lang = QComboBox()
        
        # Populate language options
        languages = self.translation_manager.get_available_languages()
        for code, name in languages.items():
            self.interface_lang.addItem(name, code)
        
        self.interface_lang.currentTextChanged.connect(self.on_language_changed)
        lang_layout.addRow(self.interface_lang_label, self.interface_lang)
        
        scroll_layout.addWidget(self.lang_group)
        
        # Playback Settings Group
        self.playback_group = QGroupBox()
        playback_layout = QFormLayout(self.playback_group)
        
        # Skip Duration
        self.skip_duration_label = QLabel()
        self.skip_duration = QSpinBox()
        self.skip_duration.setRange(1, 60)
        self.skip_duration.setValue(10)
        self.skip_duration.setSuffix(" seconds")
        playback_layout.addRow(self.skip_duration_label, self.skip_duration)
        
        # Auto-play
        self.auto_play = QCheckBox()
        playback_layout.addRow("", self.auto_play)
        
        # Remember position
        self.remember_position = QCheckBox()
        self.remember_position.setChecked(True)
        playback_layout.addRow("", self.remember_position)
        
        scroll_layout.addWidget(self.playback_group)
        
        # Audio Settings Group
        self.audio_group = QGroupBox()
        audio_layout = QFormLayout(self.audio_group)
        
        # Default Volume
        self.default_volume_label = QLabel()
        volume_layout = QHBoxLayout()
        self.default_volume = QSlider(Qt.Horizontal)
        self.default_volume.setRange(0, 100)
        self.default_volume.setValue(70)
        self.volume_label = QLabel("70%")
        self.default_volume.valueChanged.connect(
            lambda v: self.volume_label.setText(f"{v}%")
        )
        volume_layout.addWidget(self.default_volume)
        volume_layout.addWidget(self.volume_label)
        audio_layout.addRow(self.default_volume_label, volume_layout)
        
        # Volume Boost
        self.volume_boost = QCheckBox()
        audio_layout.addRow("", self.volume_boost)
        
        scroll_layout.addWidget(self.audio_group)
        
        # Theme Settings Group
        self.theme_group = QGroupBox()
        theme_layout = QVBoxLayout(self.theme_group)
        
        # Theme selection
        self.theme_label = QLabel()
        theme_layout.addWidget(self.theme_label)
        
        self.theme_buttons = QButtonGroup()
        
        # Create theme radio buttons (will be updated with translations)
        self.theme_professional = QRadioButton()
        self.theme_dark = QRadioButton()
        self.theme_light = QRadioButton()
        
        self.theme_professional.setChecked(True)  # Default
        
        self.theme_buttons.addButton(self.theme_professional, 0)
        self.theme_buttons.addButton(self.theme_dark, 1)
        self.theme_buttons.addButton(self.theme_light, 2)
        
        theme_layout.addWidget(self.theme_professional)
        theme_layout.addWidget(self.theme_dark)
        theme_layout.addWidget(self.theme_light)
        
        scroll_layout.addWidget(self.theme_group)
        
        # Set scroll widget
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Reset button
        self.reset_btn = QPushButton()
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        
        # Cancel button
        self.cancel_btn = QPushButton()
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        # Apply button
        self.apply_btn = QPushButton()
        self.apply_btn.clicked.connect(self.apply_settings)
        button_layout.addWidget(self.apply_btn)
        
        # OK button
        self.ok_btn = QPushButton()
        self.ok_btn.clicked.connect(self.accept_settings)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
    
    def update_ui_language(self):
        """Update all UI text with current language"""
        t = self.translation_manager.get_text
        
        # Window title
        self.setWindowTitle(t("settings_title"))
        
        # Main title
        self.title_label.setText(t("settings_title"))
        
        # Group titles
        self.lang_group.setTitle(t("settings_language"))
        self.playback_group.setTitle(t("settings_playback"))
        self.audio_group.setTitle(t("settings_audio"))
        self.theme_group.setTitle(t("settings_appearance"))
        
        # Labels
        self.interface_lang_label.setText(t("settings_interface_lang"))
        self.skip_duration_label.setText(t("settings_skip_duration"))
        self.default_volume_label.setText(t("settings_default_volume"))
        self.theme_label.setText(t("settings_theme"))
        
        # Checkboxes
        self.auto_play.setText(t("settings_auto_play"))
        self.remember_position.setText(t("settings_remember_position"))
        self.volume_boost.setText(t("settings_volume_boost"))
        
        # Theme radio buttons
        self.theme_professional.setText(t("settings_theme_professional"))
        self.theme_dark.setText(t("settings_theme_dark"))
        self.theme_light.setText(t("settings_theme_light"))
        
        # Buttons
        self.reset_btn.setText(t("button_reset"))
        self.cancel_btn.setText(t("button_cancel"))
        self.apply_btn.setText(t("button_apply"))
        self.ok_btn.setText(t("button_ok"))
    
    def on_language_changed(self, language_text):
        """Handle language change"""
        # Find language code from combo box
        current_index = self.interface_lang.currentIndex()
        if current_index >= 0:
            lang_code = self.interface_lang.itemData(current_index)
            if lang_code:
                # Set new language
                self.translation_manager.set_language(lang_code)
                
                # Save preference
                self.translation_manager.save_language_preference(lang_code)
                
                # Update UI
                self.update_ui_language()
                
                # Emit signal
                self.language_changed.emit(lang_code)
    
    def load_settings(self):
        """Load settings from file"""
        settings_file = "config/settings.json"
        default_settings = {
            "interface_language": self.translation_manager.current_language,
            "skip_duration": 10,
            "auto_play": False,
            "remember_position": True,
            "default_volume": 70,
            "volume_boost": False,
            "theme": "professional"
        }
        
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    default_settings.update(loaded_settings)
        except Exception as e:
            print(f"Error loading settings: {e}")
        
        return default_settings
    
    def load_current_settings(self):
        """Load current settings into UI"""
        # Set language combo
        current_lang = self.translation_manager.current_language
        languages = self.translation_manager.get_available_languages()
        for i, (code, name) in enumerate(languages.items()):
            if code == current_lang:
                self.interface_lang.setCurrentIndex(i)
                break
        
        # Load other settings
        self.skip_duration.setValue(self.settings["skip_duration"])
        self.auto_play.setChecked(self.settings["auto_play"])
        self.remember_position.setChecked(self.settings["remember_position"])
        self.default_volume.setValue(self.settings["default_volume"])
        self.volume_boost.setChecked(self.settings["volume_boost"])
        
        # Set theme
        theme = self.settings.get("theme", "professional")
        if theme == "dark":
            self.theme_dark.setChecked(True)
        elif theme == "light":
            self.theme_light.setChecked(True)
        else:
            self.theme_professional.setChecked(True)
    
    def save_settings(self):
        """Save current settings to file"""
        # Update settings from UI
        self.settings.update({
            "interface_language": self.translation_manager.current_language,
            "skip_duration": self.skip_duration.value(),
            "auto_play": self.auto_play.isChecked(),
            "remember_position": self.remember_position.isChecked(),
            "default_volume": self.default_volume.value(),
            "volume_boost": self.volume_boost.isChecked(),
            "theme": self.get_selected_theme()
        })
        
        # Save to file
        settings_file = "config/settings.json"
        os.makedirs(os.path.dirname(settings_file), exist_ok=True)
        
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving settings: {e}")
    
    def get_selected_theme(self):
        """Get currently selected theme"""
        if self.theme_dark.isChecked():
            return "dark"
        elif self.theme_light.isChecked():
            return "light"
        else:
            return "professional"
    
    def apply_settings(self):
        """Apply settings without closing dialog"""
        self.save_settings()
        self.settings_changed.emit(self.settings)
    
    def accept_settings(self):
        """Accept and apply settings"""
        self.apply_settings()
        self.accept()
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        self.interface_lang.setCurrentIndex(0)  # English
        self.skip_duration.setValue(10)
        self.auto_play.setChecked(False)
        self.remember_position.setChecked(True)
        self.default_volume.setValue(70)
        self.volume_boost.setChecked(False)
        self.theme_professional.setChecked(True)
    
    def apply_styles(self):
        """Apply beautiful styles"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                color: #ffffff;
            }
            
            QLabel {
                color: #ffffff;
                font-weight: 500;
            }
            
            QGroupBox {
                font-weight: 600;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                margin: 10px 0;
                padding-top: 15px;
                color: #ffffff;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                font-size: 14px;
                font-weight: 700;
            }
            
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                color: #ffffff;
                font-weight: 600;
                font-size: 13px;
                padding: 10px 20px;
                min-height: 20px;
            }
            
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.5);
            }
            
            QComboBox {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                color: #ffffff;
                padding: 8px;
                font-weight: 500;
                min-width: 200px;
            }
            
            QComboBox:hover {
                border-color: rgba(255, 255, 255, 0.5);
            }
            
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            
            QComboBox::down-arrow {
                image: none;
                border: none;
                width: 12px;
                height: 12px;
                background: rgba(255, 255, 255, 0.7);
            }
            
            QComboBox QAbstractItemView {
                background: rgba(255, 255, 255, 0.9);
                color: #333333;
                border: 2px solid rgba(255, 255, 255, 0.5);
                border-radius: 8px;
                selection-background-color: rgba(102, 126, 234, 0.8);
                selection-color: #ffffff;
            }
            
            QSpinBox {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                color: #ffffff;
                padding: 8px;
                font-weight: 500;
                min-width: 100px;
            }
            
            QCheckBox {
                color: #ffffff;
                font-weight: 500;
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                background: rgba(255, 255, 255, 0.1);
            }
            
            QCheckBox::indicator:checked {
                background: rgba(255, 255, 255, 0.8);
                border-color: #ffffff;
            }
            
            QRadioButton {
                color: #ffffff;
                font-weight: 500;
                spacing: 8px;
            }
            
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                background: rgba(255, 255, 255, 0.1);
            }
            
            QRadioButton::indicator:checked {
                background: rgba(255, 255, 255, 0.8);
                border-color: #ffffff;
            }
            
            QSlider::groove:horizontal {
                border: 1px solid rgba(255, 255, 255, 0.3);
                height: 8px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
            }
            
            QSlider::handle:horizontal {
                background: #ffffff;
                border: 2px solid rgba(255, 255, 255, 0.8);
                width: 20px;
                margin: -6px 0;
                border-radius: 10px;
            }
            
            QSlider::sub-page:horizontal {
                background: rgba(255, 255, 255, 0.6);
                border-radius: 4px;
            }
            
            QScrollArea {
                border: none;
                background: transparent;
            }
            
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
        """)
