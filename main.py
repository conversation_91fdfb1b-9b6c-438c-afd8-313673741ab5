#!/usr/bin/env python3
"""
VoiceSubMaster - Professional Desktop Media Player
Phase 1: Media Player Interface Foundation

Author: VoiceSubMaster Development Team
License: MIT
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.main_window_pro import MainWindowPro


def main():
    """Main application entry point"""
    # Create QApplication instance
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("VoiceSubMaster")
    app.setApplicationVersion("1.0.0-Phase1")
    app.setOrganizationName("VoiceSubMaster Team")
    app.setOrganizationDomain("voicesubmaster.com")
    
    # Set application style
    app.setStyle("Fusion")  # Modern cross-platform style
    
    # Create and show main window
    main_window = MainWindowPro()
    main_window.show()
    
    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
