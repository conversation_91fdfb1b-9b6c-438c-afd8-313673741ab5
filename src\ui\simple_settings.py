"""
Simple Settings Dialog for VoiceSubMaster
Clean and focused settings interface
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QGroupBox, QFormLayout, QCheckBox, QSlider,
    QSpinBox, QButtonGroup, QRadioButton
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
import json
import os


class SimpleSettingsDialog(QDialog):
    """Simple and clean settings dialog"""
    
    settings_changed = Signal(dict)
    language_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚙️ VoiceSubMaster Settings")
        self.setModal(True)
        self.resize(500, 400)
        self.setMinimumSize(450, 350)
        
        # Load current settings
        self.settings = self.load_settings()
        
        self.setup_ui()
        self.load_current_settings()
        self.apply_styles()
    
    def setup_ui(self):
        """Setup the settings UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title = QLabel("⚙️ Settings")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        layout.addWidget(title)
        
        # Language Settings Group
        lang_group = QGroupBox("🌐 Language Settings")
        lang_layout = QFormLayout(lang_group)
        
        # Interface Language
        self.interface_lang = QComboBox()
        self.interface_lang.addItems([
            "🇺🇸 English",
            "🇸🇦 العربية", 
            "🇫🇷 Français",
            "🇪🇸 Español"
        ])
        self.interface_lang.currentTextChanged.connect(self.on_language_changed)
        lang_layout.addRow("Interface Language:", self.interface_lang)
        
        layout.addWidget(lang_group)
        
        # Playback Settings Group
        playback_group = QGroupBox("🎬 Playback Settings")
        playback_layout = QFormLayout(playback_group)
        
        # Skip Duration
        self.skip_duration = QSpinBox()
        self.skip_duration.setRange(1, 60)
        self.skip_duration.setValue(10)
        self.skip_duration.setSuffix(" seconds")
        playback_layout.addRow("Skip Duration:", self.skip_duration)
        
        # Auto-play
        self.auto_play = QCheckBox("Auto-play when file opens")
        playback_layout.addRow("", self.auto_play)
        
        # Remember position
        self.remember_position = QCheckBox("Remember playback position")
        self.remember_position.setChecked(True)
        playback_layout.addRow("", self.remember_position)
        
        layout.addWidget(playback_group)
        
        # Audio Settings Group
        audio_group = QGroupBox("🔊 Audio Settings")
        audio_layout = QFormLayout(audio_group)
        
        # Default Volume
        volume_layout = QHBoxLayout()
        self.default_volume = QSlider(Qt.Horizontal)
        self.default_volume.setRange(0, 100)
        self.default_volume.setValue(70)
        self.volume_label = QLabel("70%")
        self.default_volume.valueChanged.connect(
            lambda v: self.volume_label.setText(f"{v}%")
        )
        volume_layout.addWidget(self.default_volume)
        volume_layout.addWidget(self.volume_label)
        audio_layout.addRow("Default Volume:", volume_layout)
        
        # Volume Boost
        self.volume_boost = QCheckBox("Enable volume boost (up to 200%)")
        audio_layout.addRow("", self.volume_boost)
        
        layout.addWidget(audio_group)
        
        # Theme Settings Group
        theme_group = QGroupBox("🎨 Appearance")
        theme_layout = QVBoxLayout(theme_group)
        
        # Theme selection
        self.theme_buttons = QButtonGroup()
        
        themes = [
            ("🌟 Professional", "Modern glassmorphism design"),
            ("🌙 Dark Mode", "Dark theme for night use"),
            ("☀️ Light Mode", "Light theme for day use")
        ]
        
        for i, (name, desc) in enumerate(themes):
            radio = QRadioButton(name)
            radio.setToolTip(desc)
            if i == 0:  # Default to Professional
                radio.setChecked(True)
            self.theme_buttons.addButton(radio, i)
            theme_layout.addWidget(radio)
        
        layout.addWidget(theme_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Reset button
        reset_btn = QPushButton("🔄 Reset")
        reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        
        # Cancel button
        cancel_btn = QPushButton("❌ Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # Apply button
        apply_btn = QPushButton("✅ Apply")
        apply_btn.clicked.connect(self.apply_settings)
        button_layout.addWidget(apply_btn)
        
        # OK button
        ok_btn = QPushButton("💾 OK")
        ok_btn.clicked.connect(self.accept_settings)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
    
    def on_language_changed(self, language_text):
        """Handle language change"""
        # Extract language code
        lang_codes = {
            "🇺🇸 English": "en",
            "🇸🇦 العربية": "ar",
            "🇫🇷 Français": "fr", 
            "🇪🇸 Español": "es"
        }
        
        lang_code = lang_codes.get(language_text, "en")
        self.language_changed.emit(lang_code)
    
    def load_settings(self):
        """Load settings from file"""
        settings_file = "config/settings.json"
        default_settings = {
            "interface_language": "🇺🇸 English",
            "skip_duration": 10,
            "auto_play": False,
            "remember_position": True,
            "default_volume": 70,
            "volume_boost": False,
            "theme": "Professional"
        }
        
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    default_settings.update(loaded_settings)
        except Exception as e:
            print(f"Error loading settings: {e}")
        
        return default_settings
    
    def load_current_settings(self):
        """Load current settings into UI"""
        self.interface_lang.setCurrentText(self.settings["interface_language"])
        self.skip_duration.setValue(self.settings["skip_duration"])
        self.auto_play.setChecked(self.settings["auto_play"])
        self.remember_position.setChecked(self.settings["remember_position"])
        self.default_volume.setValue(self.settings["default_volume"])
        self.volume_boost.setChecked(self.settings["volume_boost"])
    
    def save_settings(self):
        """Save current settings to file"""
        # Update settings from UI
        self.settings.update({
            "interface_language": self.interface_lang.currentText(),
            "skip_duration": self.skip_duration.value(),
            "auto_play": self.auto_play.isChecked(),
            "remember_position": self.remember_position.isChecked(),
            "default_volume": self.default_volume.value(),
            "volume_boost": self.volume_boost.isChecked(),
            "theme": "Professional"  # Default theme
        })
        
        # Save to file
        settings_file = "config/settings.json"
        os.makedirs(os.path.dirname(settings_file), exist_ok=True)
        
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving settings: {e}")
    
    def apply_settings(self):
        """Apply settings without closing dialog"""
        self.save_settings()
        self.settings_changed.emit(self.settings)
    
    def accept_settings(self):
        """Accept and apply settings"""
        self.apply_settings()
        self.accept()
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        self.interface_lang.setCurrentText("🇺🇸 English")
        self.skip_duration.setValue(10)
        self.auto_play.setChecked(False)
        self.remember_position.setChecked(True)
        self.default_volume.setValue(70)
        self.volume_boost.setChecked(False)
        
        # Reset theme to Professional
        self.theme_buttons.button(0).setChecked(True)
    
    def apply_styles(self):
        """Apply beautiful styles"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                color: #ffffff;
            }
            
            QLabel {
                color: #ffffff;
                font-weight: 500;
            }
            
            QGroupBox {
                font-weight: 600;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                margin: 10px 0;
                padding-top: 15px;
                color: #ffffff;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                font-size: 14px;
                font-weight: 700;
            }
            
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                color: #ffffff;
                font-weight: 600;
                font-size: 13px;
                padding: 10px 20px;
                min-height: 20px;
            }
            
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.5);
            }
            
            QComboBox {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                color: #ffffff;
                padding: 8px;
                font-weight: 500;
            }
            
            QComboBox:hover {
                border-color: rgba(255, 255, 255, 0.5);
            }
            
            QComboBox::drop-down {
                border: none;
            }
            
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
            
            QSpinBox {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                color: #ffffff;
                padding: 8px;
                font-weight: 500;
            }
            
            QCheckBox {
                color: #ffffff;
                font-weight: 500;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                background: rgba(255, 255, 255, 0.1);
            }
            
            QCheckBox::indicator:checked {
                background: rgba(255, 255, 255, 0.8);
                border-color: #ffffff;
            }
            
            QRadioButton {
                color: #ffffff;
                font-weight: 500;
            }
            
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                background: rgba(255, 255, 255, 0.1);
            }
            
            QRadioButton::indicator:checked {
                background: rgba(255, 255, 255, 0.8);
                border-color: #ffffff;
            }
            
            QSlider::groove:horizontal {
                border: 1px solid rgba(255, 255, 255, 0.3);
                height: 8px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
            }
            
            QSlider::handle:horizontal {
                background: #ffffff;
                border: 2px solid rgba(255, 255, 255, 0.8);
                width: 20px;
                margin: -6px 0;
                border-radius: 10px;
            }
            
            QSlider::sub-page:horizontal {
                background: rgba(255, 255, 255, 0.6);
                border-radius: 4px;
            }
        """)
