"""
Stylesheet definitions for VoiceSubMaster UI
"""

MAIN_WINDOW_STYLE = """
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

QMenuBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-bottom: 1px solid #555555;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #555555;
}

QMenu {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
}

QMenu::item:selected {
    background-color: #555555;
}

QStatusBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-top: 1px solid #555555;
}

QPushButton {
    background-color: #4a4a4a;
    color: #ffffff;
    border: 2px solid #666666;
    border-radius: 5px;
    padding: 8px 16px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #5a5a5a;
    border-color: #777777;
}

QPushButton:pressed {
    background-color: #3a3a3a;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    color: #666666;
    border-color: #444444;
}

QSlider::groove:horizontal {
    border: 1px solid #666666;
    height: 8px;
    background: #3c3c3c;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background: #ffffff;
    border: 1px solid #666666;
    width: 18px;
    margin: -5px 0;
    border-radius: 9px;
}

QSlider::handle:horizontal:hover {
    background: #cccccc;
}

QLabel {
    color: #ffffff;
}

QVideoWidget {
    background-color: #000000;
    border: 2px solid #333333;
    border-radius: 5px;
}
"""
