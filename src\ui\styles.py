"""
Stylesheet definitions for VoiceSubMaster UI
"""

MAIN_WINDOW_STYLE = """
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

QMenuBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-bottom: 1px solid #555555;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #555555;
}

QMenu {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
}

QMenu::item:selected {
    background-color: #555555;
}

QStatusBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-top: 1px solid #555555;
}

QPushButton {
    background-color: transparent;
    color: #ffffff;
    border: none;
    border-radius: 12px;
    padding: 8px;
    font-size: 24px;
    font-weight: bold;
    min-width: 60px;
}

QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.2);
}

QPushButton:disabled {
    background-color: transparent;
    color: rgba(255, 255, 255, 0.5);
}

QSlider::groove:horizontal {
    border: 1px solid #666666;
    height: 8px;
    background: #3c3c3c;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background: #ffffff;
    border: 1px solid #666666;
    width: 18px;
    margin: -5px 0;
    border-radius: 9px;
}

QSlider::handle:horizontal:hover {
    background: #cccccc;
}

QLabel {
    color: #ffffff;
}

QVideoWidget {
    background-color: #000000;
    border: 2px solid #333333;
    border-radius: 5px;
}
"""
