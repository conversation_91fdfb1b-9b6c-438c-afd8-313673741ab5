"""
Professional Main Window for VoiceSubMaster Application
Enhanced with modern icons, advanced settings, and professional audio controls
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QMenuBar, QStatusBar, QFileDialog, QMessageBox,
    QPushButton, QSlider, QLabel, QFrame, QSplitter,
    QTabWidget, QToolBar, QSystemTrayIcon
)
from PySide6.QtCore import Qt, QUrl, QTimer, Signal, QSize
from PySide6.QtGui import QAction, QKeySequence, QIcon, QPixmap
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
from .styles import MAIN_WINDOW_STYLE
from .welcome_widget import WelcomeWidget
from .settings_dialog import SettingsDialog
from .audio_controls import AudioControlsWidget
from .icons import icon_manager


class MainWindowPro(QMainWindow):
    """Professional main application window with enhanced features"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VoiceSubMaster Pro - Professional Media Player")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1000, 700)
        
        # Initialize media player components
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        
        # Initialize UI components
        self.video_widget = None
        self.audio_controls = None
        self.settings_dialog = None
        
        # Timer for updating position
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self.update_position)
        self.position_timer.start(1000)
        
        # Current file and settings
        self.current_file = None
        self.is_fullscreen = False
        
        # Setup UI
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
        self.setup_shortcuts()
        self.apply_styles()
        
        # System tray
        self.setup_system_tray()
        
    def setup_ui(self):
        """Setup the professional user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Video and controls
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Audio controls and settings
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions (70% left, 30% right)
        splitter.setSizes([1000, 400])
        
        # Set video output
        self.media_player.setVideoOutput(self.video_widget)
    
    def create_left_panel(self):
        """Create left panel with video and main controls"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # Welcome widget
        self.welcome_widget = WelcomeWidget()
        self.welcome_widget.language_changed.connect(self.on_language_changed)
        self.welcome_widget.mode_changed.connect(self.on_mode_changed)
        layout.addWidget(self.welcome_widget)
        
        # Video container
        video_container = self.create_video_container()
        layout.addWidget(video_container)
        
        # Main controls
        controls_container = self.create_main_controls()
        layout.addWidget(controls_container)
        
        return panel
    
    def create_right_panel(self):
        """Create right panel with audio controls and info"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Tab widget for different control panels
        tab_widget = QTabWidget()
        
        # Audio controls tab
        self.audio_controls = AudioControlsWidget()
        self.audio_controls.volume_changed.connect(self.set_volume)
        tab_widget.addTab(self.audio_controls, "🔊 Audio")
        
        # Playlist tab (placeholder for future)
        playlist_widget = QLabel("Playlist functionality\ncoming in Phase 8!")
        playlist_widget.setAlignment(Qt.AlignCenter)
        playlist_widget.setStyleSheet("color: #7F8C8D; font-style: italic;")
        tab_widget.addTab(playlist_widget, "📋 Playlist")
        
        # Subtitles tab (placeholder for future)
        subtitles_widget = QLabel("Subtitle functionality\ncoming in Phase 6!")
        subtitles_widget.setAlignment(Qt.AlignCenter)
        subtitles_widget.setStyleSheet("color: #7F8C8D; font-style: italic;")
        tab_widget.addTab(subtitles_widget, "📝 Subtitles")
        
        layout.addWidget(tab_widget)
        
        return panel
    
    def create_video_container(self):
        """Create video widget container"""
        container = QWidget()
        container.setObjectName("videoContainer")
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Video widget
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumHeight(400)
        layout.addWidget(self.video_widget)
        
        return container
    
    def create_main_controls(self):
        """Create main playback controls"""
        container = QWidget()
        container.setObjectName("controlsContainer")
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Position controls
        position_layout = self.create_position_controls()
        layout.addLayout(position_layout)
        
        # Main buttons
        buttons_layout = self.create_main_buttons()
        layout.addLayout(buttons_layout)
        
        # Quick volume control
        volume_layout = self.create_quick_volume()
        layout.addLayout(volume_layout)
        
        return container
    
    def create_position_controls(self):
        """Create position slider and time labels"""
        layout = QVBoxLayout()
        
        # Time and position
        time_layout = QHBoxLayout()
        
        self.time_label = QLabel("00:00")
        self.time_label.setObjectName("timeLabel")
        time_layout.addWidget(self.time_label)
        
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 0)
        self.position_slider.sliderMoved.connect(self.set_position)
        time_layout.addWidget(self.position_slider)
        
        self.duration_label = QLabel("00:00")
        self.duration_label.setObjectName("durationLabel")
        time_layout.addWidget(self.duration_label)
        
        layout.addLayout(time_layout)
        
        return layout
    
    def create_main_buttons(self):
        """Create main control buttons with icons"""
        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(10)
        
        # Previous/Backward button
        self.backward_button = QPushButton(" -10s")
        self.backward_button.setIcon(icon_manager.get_icon("skip_previous", 24))
        self.backward_button.clicked.connect(self.backward_10s)
        self.backward_button.setMinimumSize(100, 50)
        self.backward_button.setToolTip("Skip backward 10 seconds (Left Arrow)")
        layout.addWidget(self.backward_button)
        
        # Play button
        self.play_button = QPushButton(" Play")
        self.play_button.setIcon(icon_manager.get_icon("play", 28, "#FFFFFF"))
        self.play_button.setObjectName("playButton")
        self.play_button.clicked.connect(self.play_media)
        self.play_button.setMinimumSize(120, 50)
        self.play_button.setToolTip("Play media (Spacebar)")
        layout.addWidget(self.play_button)
        
        # Pause button
        self.pause_button = QPushButton(" Pause")
        self.pause_button.setIcon(icon_manager.get_icon("pause", 28, "#FFFFFF"))
        self.pause_button.setObjectName("pauseButton")
        self.pause_button.clicked.connect(self.pause_media)
        self.pause_button.setMinimumSize(120, 50)
        self.pause_button.setEnabled(False)
        self.pause_button.setToolTip("Pause media (Spacebar)")
        layout.addWidget(self.pause_button)
        
        # Stop button
        self.stop_button = QPushButton(" Stop")
        self.stop_button.setIcon(icon_manager.get_icon("stop", 24))
        self.stop_button.clicked.connect(self.stop_media)
        self.stop_button.setMinimumSize(100, 50)
        self.stop_button.setToolTip("Stop playback")
        layout.addWidget(self.stop_button)
        
        # Next/Forward button
        self.forward_button = QPushButton(" +10s")
        self.forward_button.setIcon(icon_manager.get_icon("skip_next", 24))
        self.forward_button.clicked.connect(self.forward_10s)
        self.forward_button.setMinimumSize(100, 50)
        self.forward_button.setToolTip("Skip forward 10 seconds (Right Arrow)")
        layout.addWidget(self.forward_button)
        
        return layout
    
    def create_quick_volume(self):
        """Create quick volume control"""
        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        
        # Volume icon
        volume_icon = QLabel()
        volume_icon.setPixmap(icon_manager.get_icon("volume_up", 20).pixmap(20, 20))
        layout.addWidget(volume_icon)
        
        # Quick volume slider
        self.quick_volume_slider = QSlider(Qt.Horizontal)
        self.quick_volume_slider.setRange(0, 100)
        self.quick_volume_slider.setValue(70)
        self.quick_volume_slider.setMaximumWidth(200)
        self.quick_volume_slider.valueChanged.connect(self.set_volume)
        layout.addWidget(self.quick_volume_slider)
        
        # Volume percentage
        self.quick_volume_label = QLabel("70%")
        self.quick_volume_label.setMinimumWidth(40)
        layout.addWidget(self.quick_volume_label)
        
        return layout

    # Media control methods (same as before but with additional features)
    def open_file(self):
        """Open a media file"""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_dialog.setNameFilter(
            "Media Files (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v "
            "*.mp3 *.wav *.flac *.aac *.ogg *.wma *.m4a);;All Files (*)"
        )

        if file_dialog.exec():
            file_path = file_dialog.selectedFiles()[0]
            self.load_media(file_path)

    def load_media(self, file_path):
        """Load media file into player"""
        if not os.path.exists(file_path):
            QMessageBox.warning(self, "Error", f"File not found: {file_path}")
            return

        self.current_file = file_path
        file_url = QUrl.fromLocalFile(file_path)
        self.media_player.setSource(file_url)

        # Update status bar and UI
        file_name = Path(file_path).name
        self.status_bar.showMessage(f"Loaded: {file_name}")

        # Update audio info
        if self.audio_controls:
            file_info = f"File: {file_name}\nPath: {file_path}"
            self.audio_controls.update_audio_info(file_info)

        # Enable controls
        self.play_button.setEnabled(True)
        self.forward_button.setEnabled(True)
        self.backward_button.setEnabled(True)
        self.stop_button.setEnabled(True)

    def play_media(self):
        """Start media playback"""
        if self.current_file:
            self.media_player.play()

    def pause_media(self):
        """Pause media playback"""
        self.media_player.pause()

    def stop_media(self):
        """Stop media playback"""
        self.media_player.stop()

    def toggle_play_pause(self):
        """Toggle between play and pause"""
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            self.pause_media()
        else:
            self.play_media()

    def forward_10s(self):
        """Skip forward 10 seconds"""
        current_position = self.media_player.position()
        new_position = min(current_position + 10000, self.media_player.duration())
        self.media_player.setPosition(new_position)

    def backward_10s(self):
        """Skip backward 10 seconds"""
        current_position = self.media_player.position()
        new_position = max(current_position - 10000, 0)
        self.media_player.setPosition(new_position)

    def set_volume(self, value):
        """Set audio volume"""
        volume = value / 100.0
        self.audio_output.setVolume(volume)
        self.quick_volume_label.setText(f"{value}%")
        self.quick_volume_slider.setValue(value)

    def volume_up(self):
        """Increase volume by 10%"""
        current_volume = self.quick_volume_slider.value()
        new_volume = min(current_volume + 10, 100)
        self.set_volume(new_volume)

    def volume_down(self):
        """Decrease volume by 10%"""
        current_volume = self.quick_volume_slider.value()
        new_volume = max(current_volume - 10, 0)
        self.set_volume(new_volume)

    def set_position(self, position):
        """Set playback position"""
        self.media_player.setPosition(position)

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.is_fullscreen:
            self.showNormal()
            self.is_fullscreen = False
        else:
            self.showFullScreen()
            self.is_fullscreen = True

    def toggle_always_on_top(self, checked):
        """Toggle always on top"""
        if checked:
            self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        else:
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowStaysOnTopHint)
        self.show()

    def open_settings(self):
        """Open settings dialog"""
        if not self.settings_dialog:
            self.settings_dialog = SettingsDialog(self)
            self.settings_dialog.settings_changed.connect(self.apply_settings)

        self.settings_dialog.exec()

    def apply_settings(self, settings):
        """Apply settings changes"""
        # Apply volume settings
        if "default_volume" in settings:
            self.set_volume(settings["default_volume"])

        # Apply other settings as needed
        self.status_bar.showMessage("Settings applied successfully")

    def show_volume_popup(self):
        """Show volume popup (placeholder)"""
        QMessageBox.information(self, "Volume", "Advanced volume controls in the Audio tab!")

    # Event handlers (same as before)
    def media_status_changed(self, status):
        """Handle media status changes"""
        if status == QMediaPlayer.LoadedMedia:
            self.status_bar.showMessage("Media loaded successfully")
            self.codec_label.setText("Ready")
        elif status == QMediaPlayer.InvalidMedia:
            self.status_bar.showMessage("Invalid media file")
            QMessageBox.warning(self, "Error", "Cannot load this media file")

    def playback_state_changed(self, state):
        """Handle playback state changes"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.status_bar.showMessage("Playing...")
        else:
            self.play_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            if state == QMediaPlayer.PausedState:
                self.status_bar.showMessage("Paused")
            elif state == QMediaPlayer.StoppedState:
                self.status_bar.showMessage("Stopped")

    def duration_changed(self, duration):
        """Handle duration changes"""
        self.position_slider.setRange(0, duration)
        self.duration_label.setText(self.format_time(duration))

    def position_changed(self, position):
        """Handle position changes"""
        self.position_slider.setValue(position)
        self.time_label.setText(self.format_time(position))

    def update_position(self):
        """Update position display"""
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            position = self.media_player.position()
            self.position_slider.setValue(position)
            self.time_label.setText(self.format_time(position))

    def handle_error(self, error):
        """Handle media player errors"""
        error_messages = {
            QMediaPlayer.NoError: "No error",
            QMediaPlayer.ResourceError: "Resource error - file not found or corrupted",
            QMediaPlayer.FormatError: "Format error - unsupported file format",
            QMediaPlayer.NetworkError: "Network error",
            QMediaPlayer.AccessDeniedError: "Access denied - insufficient permissions"
        }

        error_msg = error_messages.get(error, f"Unknown error: {error}")
        self.status_bar.showMessage(f"Error: {error_msg}")
        QMessageBox.critical(self, "Media Player Error", error_msg)

    # Utility methods
    def format_time(self, milliseconds):
        """Format time in milliseconds to MM:SS format"""
        seconds = milliseconds // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About VoiceSubMaster Pro",
            "<h3>VoiceSubMaster Pro v1.0.0-Phase1</h3>"
            "<p>Professional Desktop Media Player</p>"
            "<p><b>Enhanced Features:</b></p>"
            "<ul>"
            "<li>Modern professional interface</li>"
            "<li>Advanced audio controls with equalizer</li>"
            "<li>Comprehensive settings</li>"
            "<li>System tray integration</li>"
            "<li>Keyboard shortcuts</li>"
            "<li>Fullscreen support</li>"
            "</ul>"
            "<p>Built with PySide6 and Qt</p>"
        )

    def apply_styles(self):
        """Apply custom styles to the application"""
        self.setStyleSheet(MAIN_WINDOW_STYLE)

    # Event handlers for welcome widget
    def on_language_changed(self, language):
        """Handle language change"""
        self.status_bar.showMessage(f"Language changed to: {language}")

    def on_mode_changed(self, mode):
        """Handle mode change"""
        self.status_bar.showMessage(f"Mode changed to: {mode}")

    def setup_menu_bar(self):
        """Setup professional menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        # Open file
        open_action = QAction("&Open File...", self)
        open_action.setIcon(icon_manager.get_icon("folder_open", 16))
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        # Recent files (placeholder)
        recent_menu = file_menu.addMenu("Recent Files")
        recent_menu.addAction("No recent files")

        file_menu.addSeparator()

        # Exit
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # View menu
        view_menu = menubar.addMenu("&View")

        # Fullscreen
        fullscreen_action = QAction("&Fullscreen", self)
        fullscreen_action.setIcon(icon_manager.get_icon("fullscreen", 16))
        fullscreen_action.setShortcut("F11")
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        view_menu.addSeparator()

        # Always on top
        self.always_on_top_action = QAction("Always on &Top", self)
        self.always_on_top_action.setCheckable(True)
        self.always_on_top_action.triggered.connect(self.toggle_always_on_top)
        view_menu.addAction(self.always_on_top_action)

        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        # Settings
        settings_action = QAction("&Settings...", self)
        settings_action.setIcon(icon_manager.get_icon("settings", 16))
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(self.open_settings)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menubar.addMenu("&Help")

        # About
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """Setup main toolbar"""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)

        # Open file
        toolbar.addAction(icon_manager.get_icon("folder_open", 24), "Open", self.open_file)

        toolbar.addSeparator()

        # Playback controls
        toolbar.addAction(icon_manager.get_icon("skip_previous", 24), "Previous", self.backward_10s)
        toolbar.addAction(icon_manager.get_icon("play", 24), "Play", self.play_media)
        toolbar.addAction(icon_manager.get_icon("pause", 24), "Pause", self.pause_media)
        toolbar.addAction(icon_manager.get_icon("stop", 24), "Stop", self.stop_media)
        toolbar.addAction(icon_manager.get_icon("skip_next", 24), "Next", self.forward_10s)

        toolbar.addSeparator()

        # Volume
        toolbar.addAction(icon_manager.get_icon("volume_up", 24), "Volume", self.show_volume_popup)

        toolbar.addSeparator()

        # Settings
        toolbar.addAction(icon_manager.get_icon("settings", 24), "Settings", self.open_settings)

        # Fullscreen
        toolbar.addAction(icon_manager.get_icon("fullscreen", 24), "Fullscreen", self.toggle_fullscreen)

    def setup_status_bar(self):
        """Setup status bar with additional info"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("Ready - Open a media file to start")

        # Add permanent widgets to status bar
        self.codec_label = QLabel("No media")
        self.status_bar.addPermanentWidget(self.codec_label)

        self.resolution_label = QLabel("")
        self.status_bar.addPermanentWidget(self.resolution_label)

    def setup_system_tray(self):
        """Setup system tray icon"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)
            self.tray_icon.setIcon(icon_manager.get_icon("play", 16))
            self.tray_icon.setToolTip("VoiceSubMaster Pro")
            self.tray_icon.show()

    def setup_connections(self):
        """Setup signal/slot connections"""
        # Media player connections
        self.media_player.mediaStatusChanged.connect(self.media_status_changed)
        self.media_player.playbackStateChanged.connect(self.playback_state_changed)
        self.media_player.durationChanged.connect(self.duration_changed)
        self.media_player.positionChanged.connect(self.position_changed)
        self.media_player.errorOccurred.connect(self.handle_error)

        # Set initial volume
        self.set_volume(70)

    def setup_shortcuts(self):
        """Setup keyboard shortcuts"""
        # Spacebar for play/pause
        play_pause_shortcut = QAction(self)
        play_pause_shortcut.setShortcut(Qt.Key_Space)
        play_pause_shortcut.triggered.connect(self.toggle_play_pause)
        self.addAction(play_pause_shortcut)

        # Arrow keys
        left_shortcut = QAction(self)
        left_shortcut.setShortcut(Qt.Key_Left)
        left_shortcut.triggered.connect(self.backward_10s)
        self.addAction(left_shortcut)

        right_shortcut = QAction(self)
        right_shortcut.setShortcut(Qt.Key_Right)
        right_shortcut.triggered.connect(self.forward_10s)
        self.addAction(right_shortcut)

        up_shortcut = QAction(self)
        up_shortcut.setShortcut(Qt.Key_Up)
        up_shortcut.triggered.connect(self.volume_up)
        self.addAction(up_shortcut)

        down_shortcut = QAction(self)
        down_shortcut.setShortcut(Qt.Key_Down)
        down_shortcut.triggered.connect(self.volume_down)
        self.addAction(down_shortcut)

        # F11 for fullscreen
        f11_shortcut = QAction(self)
        f11_shortcut.setShortcut(Qt.Key_F11)
        f11_shortcut.triggered.connect(self.toggle_fullscreen)
        self.addAction(f11_shortcut)
