# VoiceSubMaster

A professional desktop application for offline media playback with integrated speech-to-text, translation, and text-to-speech capabilities.

## Features (Phase 1) ✅ COMPLETED - PROFESSIONAL EDITION

### 🎬 **Enhanced Video Experience**
- ✅ **Full-width video display** - Video takes up the entire width for immersive viewing
- ✅ **Professional media player interface** using PySide6 with modern SVG icons
- ✅ **Comprehensive format support** (MP4, AVI, MKV, MOV, MP3, WAV, FLAC, etc.)
- ✅ **Fullscreen mode** with F11 toggle

### 🎮 **Advanced Controls**
- ✅ **Enhanced control buttons** with professional icons and tooltips:
  - Play, Pause, Stop, Forward 10s, Backward 10s, Fullscreen
- ✅ **Professional toolbar** with quick access to all functions
- ✅ **Enhanced keyboard shortcuts**:
  - Spacebar: Play/Pause toggle
  - Left/Right arrows: Skip backward/forward 10 seconds
  - Up/Down arrows: Volume control
  - F11: Fullscreen toggle
  - Ctrl+O: Open file, Ctrl+,: Settings

### 📋 **Smart Playlist Management**
- ✅ **Advanced playlist system** with drag & drop support
- ✅ **Add multiple files** with + button
- ✅ **Previous/Next navigation** with shuffle and repeat modes
- ✅ **Context menu** for file management and removal

### 🔊 **Professional Audio Controls**
- ✅ **10-band equalizer** with presets (Rock, Pop, Jazz, Classical, Electronic, etc.)
- ✅ **Audio effects**: Bass Boost, Treble Enhancement, Virtual Surround, Noise Reduction
- ✅ **Volume boost** up to 200% with safety warnings
- ✅ **Smart volume button** that changes icon based on level

### 🌍 **Multi-Language Support**
- ✅ **4 languages supported**: English 🇺🇸, Arabic 🇸🇦, French 🇫🇷, Spanish 🇪🇸
- ✅ **Language-specific welcome messages** and UI translations
- ✅ **Auto-detect and translation settings** for future phases

### ⚙️ **Comprehensive Settings**
- ✅ **5-tab settings dialog**: General, Playback, Audio, Interface, Advanced
- ✅ **Hardware acceleration** and performance options
- ✅ **Theme and appearance** customization
- ✅ **Always on top** and system tray integration

### 🎨 **Professional Design**
- ✅ **Modern flat design** with professional styling and hover effects
- ✅ **Resizable panels** for customized workspace
- ✅ **Enhanced status bar** with codec and resolution information
- ✅ **Error handling** and comprehensive user feedback

## Requirements
- Python 3.9+
- PySide6
- FFmpeg (for media support)

## Installation
```bash
pip install -r requirements.txt
```

## Usage
```bash
# Run the professional application
python main.py

# Or use the batch file (Windows)
run.bat

# Run tests
python tests/test_main_window.py
```

## 🎯 **How to Use the New Features**

### **📋 Playlist Management**
1. Click the **"📋 Playlist"** tab in the right panel
2. Click **"+ Add Files"** to select multiple media files
3. Double-click any file to play it
4. Use **Previous/Next** buttons or right-click for context menu
5. Enable **Shuffle 🔀** or **Repeat 🔁** modes

### **🔊 Audio Enhancement**
1. Go to **"🔊 Audio"** tab for advanced controls
2. Use the **10-band equalizer** or select presets
3. Enable audio effects like **Bass Boost** or **Virtual Surround**
4. Adjust volume up to **200%** with boost mode

### **🌍 Language Settings**
1. Click **"⚙️ Language"** tab to change interface language
2. Select from **English, Arabic, French, or Spanish**
3. Choose between **Text** or **Voice** input modes
4. Configure auto-detection and translation preferences

### **⚙️ Advanced Settings**
1. Go to **Tools → Settings** or press **Ctrl+,**
2. Explore 5 categories of settings:
   - **General**: Language and file associations
   - **Playback**: Speed, quality, aspect ratio
   - **Audio**: Output device, channels, volume
   - **Interface**: Theme, window behavior
   - **Advanced**: Performance, logging, cache

### **🎬 Video Experience**
- Press **F11** for fullscreen mode
- Use **toolbar** for quick access to functions
- **Right-click** video area for context menu
- **Drag & drop** files directly onto the application

## Keyboard Shortcuts
- **Spacebar**: Play/Pause toggle
- **Left Arrow**: Skip backward 10 seconds
- **Right Arrow**: Skip forward 10 seconds
- **Up Arrow**: Increase volume by 10%
- **Down Arrow**: Decrease volume by 10%
- **Ctrl+O**: Open file dialog

## Supported Media Formats
- **Video**: MP4, AVI, MKV, MOV, WMV, FLV, WebM, M4V
- **Audio**: MP3, WAV, FLAC, AAC, OGG, WMA, M4A

## Development Phases
- ✅ Phase 1: Media Player Interface Foundation
- ⏳ Phase 2: Whisper Speech-to-Text Integration
- ⏳ Phase 3: Offline Translation System
- ⏳ Phase 4: Text-to-Speech Integration
- ⏳ Phase 5: Speaker Identification & Voice Assignment
- ⏳ Phase 6: OCR for On-Screen Text
- ⏳ Phase 7: Comprehensive Settings GUI
- ⏳ Phase 8: Folder Playlist Functionality
- ⏳ Phase 9: Export Capabilities
- ⏳ Phase 10: Final Executable Build

## License
MIT License
