# VoiceSubMaster

A professional desktop application for offline media playback with integrated speech-to-text, translation, and text-to-speech capabilities.

## Screenshots
![VoiceSubMaster Interface](assets/ui_main_interface.png)
*Modern flat design interface inspired by professional translation applications*

## Features (Phase 1) ✅ COMPLETED
- ✅ Professional media player interface using PySide6
- ✅ Support for video and audio playback (MP4, AVI, MKV, MOV, MP3, WAV, FLAC, etc.)
- ✅ Keyboard shortcuts for easy control:
  - Spacebar: Play/Pause toggle
  - Left/Right arrows: Skip backward/forward 10 seconds
  - Up/Down arrows: Volume control
- ✅ Control buttons: Play, Pause, Forward 10s, Backward 10s, Volume Up/Down
- ✅ File menu with "Open File" option and format filters
- ✅ Status bar showing playback status and file information
- ✅ Progress bar for playback position with time display
- ✅ Clean, modern flat UI design with professional styling inspired by modern translation apps
- ✅ Beautiful color scheme: Light background (#ECF0F1), Blue accents (#3498DB), Professional typography
- ✅ Welcome widget with bilingual support (Arabic/English)
- ✅ Language and mode selection buttons with hover effects
- ✅ Rounded corners, shadows, and modern flat design elements
- ✅ Error handling for unsupported file formats
- ✅ Unit tests for core functionality

## Requirements
- Python 3.9+
- PySide6
- FFmpeg (for media support)

## Installation
```bash
pip install -r requirements.txt
```

## Usage
```bash
# Run the application
python main.py

# Or use the batch file (Windows)
run.bat

# Run tests
python tests/test_main_window.py
```

## Keyboard Shortcuts
- **Spacebar**: Play/Pause toggle
- **Left Arrow**: Skip backward 10 seconds
- **Right Arrow**: Skip forward 10 seconds
- **Up Arrow**: Increase volume by 10%
- **Down Arrow**: Decrease volume by 10%
- **Ctrl+O**: Open file dialog

## Supported Media Formats
- **Video**: MP4, AVI, MKV, MOV, WMV, FLV, WebM, M4V
- **Audio**: MP3, WAV, FLAC, AAC, OGG, WMA, M4A

## Development Phases
- ✅ Phase 1: Media Player Interface Foundation
- ⏳ Phase 2: Whisper Speech-to-Text Integration
- ⏳ Phase 3: Offline Translation System
- ⏳ Phase 4: Text-to-Speech Integration
- ⏳ Phase 5: Speaker Identification & Voice Assignment
- ⏳ Phase 6: OCR for On-Screen Text
- ⏳ Phase 7: Comprehensive Settings GUI
- ⏳ Phase 8: Folder Playlist Functionality
- ⏳ Phase 9: Export Capabilities
- ⏳ Phase 10: Final Executable Build

## License
MIT License
