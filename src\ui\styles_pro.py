"""
Professional Ultra-Modern Stylesheet for VoiceSubMaster
Glassmorphism + Gradient Design with Professional Icons
"""

PROFESSIONAL_STYLE = """
/* ===== MAIN APPLICATION WINDOW ===== */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
    color: #ffffff;
    font-family: 'Segoe UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
    font-weight: 500;
}

QWidget {
    background-color: transparent;
    color: #ffffff;
}

/* ===== GLASSMORPHISM MENU BAR ===== */
QMenuBar {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    color: #ffffff;
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px;
    font-weight: 600;
    font-size: 14px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 12px 20px;
    border-radius: 12px;
    margin: 4px 6px;
    transition: all 0.3s ease;
}

QMenuBar::item:selected {
    background: rgba(255, 255, 255, 0.25);
    color: #ffffff;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

QMenuBar::item:pressed {
    background: rgba(255, 255, 255, 0.35);
    transform: scale(0.98);
}

/* ===== DROPDOWN MENUS ===== */
QMenu {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(30px);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 12px;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
}

QMenu::item {
    padding: 14px 24px;
    border-radius: 10px;
    margin: 3px;
    font-weight: 500;
    font-size: 13px;
}

QMenu::item:selected {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
}

QMenu::separator {
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
    margin: 8px 20px;
}

/* ===== PROFESSIONAL BUTTONS ===== */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(255, 255, 255, 0.2), 
        stop:1 rgba(255, 255, 255, 0.1));
    backdrop-filter: blur(20px);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 14px 24px;
    font-weight: 600;
    font-size: 14px;
    min-height: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(255, 255, 255, 0.3), 
        stop:1 rgba(255, 255, 255, 0.2));
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

QPushButton:pressed {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(0px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

QPushButton:disabled {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: none;
}

/* ===== SPECIAL BUTTON STYLES ===== */
QPushButton#playButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #00f260, stop:1 #0575e6);
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    font-weight: 700;
}

QPushButton#playButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #00f260, stop:1 #0575e6);
    box-shadow: 0 15px 50px rgba(0, 242, 96, 0.4);
}

QPushButton#pauseButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #ff6b6b, stop:1 #ee5a24);
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
    font-weight: 700;
}

QPushButton#pauseButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #ff6b6b, stop:1 #ee5a24);
    box-shadow: 0 15px 50px rgba(255, 107, 107, 0.4);
}

/* ===== MODERN SLIDERS ===== */
QSlider::groove:horizontal {
    border: none;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    backdrop-filter: blur(10px);
}

QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 2px solid rgba(255, 255, 255, 0.8);
    width: 24px;
    height: 24px;
    margin: -8px 0;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

QSlider::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #667eea, stop:1 #764ba2);
    border-color: #ffffff;
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
    transform: scale(1.1);
}

QSlider::sub-page:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #667eea, stop:1 #764ba2);
    border-radius: 4px;
}

QSlider::add-page:horizontal {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

/* ===== PROFESSIONAL LABELS ===== */
QLabel {
    color: #ffffff;
    font-weight: 500;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

QLabel#timeLabel, QLabel#durationLabel {
    color: rgba(255, 255, 255, 0.9);
    font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
    font-size: 13px;
    font-weight: 600;
    background: rgba(0, 0, 0, 0.2);
    padding: 6px 12px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

QLabel#volumeLabel {
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    font-weight: 600;
}

/* ===== VIDEO WIDGET ===== */
QVideoWidget {
    background-color: #000000;
    border: 3px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* ===== CONTAINER WIDGETS ===== */
QWidget#controlsContainer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
}

QWidget#videoContainer {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 15px;
    margin: 10px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

/* ===== STATUS BAR ===== */
QStatusBar {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    color: rgba(255, 255, 255, 0.9);
    border: none;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 10px;
    font-size: 12px;
    font-weight: 500;
}

/* ===== TOOLBAR ===== */
QToolBar {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(25px);
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px;
    spacing: 12px;
}

QToolBar::handle {
    background: rgba(255, 255, 255, 0.3);
    width: 4px;
    border-radius: 2px;
    margin: 4px;
}

QToolBar QToolButton {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 10px;
    margin: 2px;
    color: #ffffff;
    font-weight: 600;
}

QToolBar QToolButton:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

QToolBar QToolButton:pressed {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(0.95);
}

/* ===== SCROLL AREAS ===== */
QScrollArea {
    background: transparent;
    border: none;
}

QScrollBar:vertical {
    background: rgba(255, 255, 255, 0.1);
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* ===== ANIMATIONS ===== */
* {
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

QPushButton, QToolButton {
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

QSlider::handle {
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}
"""
