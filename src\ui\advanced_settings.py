"""
Advanced Professional Settings Dialog for VoiceSubMaster
Ultra-comprehensive settings with professional UI
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QSlider, QSpinBox, QComboBox, QCheckBox, QPushButton,
    QGroupBox, QFormLayout, QColorDialog, QFontDialog, QFileDialog,
    QLineEdit, QTextEdit, QScrollArea, QFrame, QButtonGroup,
    QRadioButton, QProgressBar, QSplitter, QTreeWidget, QTreeWidgetItem,
    QListWidget, QListWidgetItem, QTableWidget, QTableWidgetItem
)
from PySide6.QtCore import Qt, Signal, QSettings, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QColor, QPalette, QPixmap, QPainter
import json
import os
from .icons_pro import pro_icon_manager


class AdvancedSettingsDialog(QDialog):
    """Ultra-comprehensive professional settings dialog"""
    
    settings_changed = Signal(dict)
    theme_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🎛️ VoiceSubMaster Pro - Advanced Settings")
        self.setModal(True)
        self.resize(900, 700)
        self.setMinimumSize(800, 600)
        
        # Load current settings
        self.settings = self.load_settings()
        
        self.setup_ui()
        self.load_current_settings()
        self.apply_professional_styles()
        
        # Animation for smooth transitions
        self.setup_animations()
    
    def setup_ui(self):
        """Setup the advanced settings UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(0)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Header
        header = self.create_header()
        layout.addWidget(header)
        
        # Main content with splitter
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # Left sidebar with categories
        sidebar = self.create_sidebar()
        splitter.addWidget(sidebar)
        
        # Right content area
        self.content_stack = QTabWidget()
        self.content_stack.setTabPosition(QTabWidget.North)
        self.content_stack.setTabsClosable(False)
        splitter.addWidget(self.content_stack)
        
        # Create all setting tabs
        self.create_all_tabs()
        
        # Set splitter proportions
        splitter.setSizes([250, 650])
        
        # Footer with buttons
        footer = self.create_footer()
        layout.addWidget(footer)
    
    def create_header(self):
        """Create professional header"""
        header = QFrame()
        header.setObjectName("headerFrame")
        header.setFixedHeight(80)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(30, 20, 30, 20)
        
        # Icon and title
        icon_label = QLabel()
        icon_pixmap = pro_icon_manager.get_icon("settings", 48, "#ffffff", "blue").pixmap(48, 48)
        icon_label.setPixmap(icon_pixmap)
        layout.addWidget(icon_label)
        
        title_layout = QVBoxLayout()
        title = QLabel("Advanced Settings")
        title.setObjectName("headerTitle")
        subtitle = QLabel("Customize your VoiceSubMaster Pro experience")
        subtitle.setObjectName("headerSubtitle")
        
        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        layout.addLayout(title_layout)
        
        layout.addStretch()
        
        # Quick language switcher
        lang_layout = QVBoxLayout()
        lang_label = QLabel("Language:")
        lang_label.setObjectName("quickLangLabel")

        self.quick_lang_combo = QComboBox()
        self.quick_lang_combo.addItems(["🇺🇸 English", "🇸🇦 العربية", "🇫🇷 Français", "🇪🇸 Español"])
        self.quick_lang_combo.currentTextChanged.connect(self.quick_language_change)

        lang_layout.addWidget(lang_label)
        lang_layout.addWidget(self.quick_lang_combo)
        layout.addLayout(lang_layout)

        # Quick theme switcher
        theme_layout = QVBoxLayout()
        theme_label = QLabel("Theme:")
        theme_label.setObjectName("quickThemeLabel")

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["🌟 Professional", "🌙 Dark Mode", "☀️ Light Mode", "🎨 Custom"])
        self.theme_combo.currentTextChanged.connect(self.quick_theme_change)

        theme_layout.addWidget(theme_label)
        theme_layout.addWidget(self.theme_combo)
        layout.addLayout(theme_layout)
        
        return header
    
    def create_sidebar(self):
        """Create settings categories sidebar"""
        sidebar = QFrame()
        sidebar.setObjectName("sidebarFrame")
        sidebar.setFixedWidth(250)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)
        
        # Categories
        categories = [
            ("🎬", "Media & Playback", "Configure video and audio playback settings"),
            ("🔊", "Audio System", "Advanced audio controls and effects"),
            ("🎨", "Appearance", "Themes, colors, and visual customization"),
            ("🌐", "Languages", "Interface and content language settings"),
            ("⚡", "Performance", "Optimization and hardware acceleration"),
            ("🔒", "Privacy & Security", "Data protection and security options"),
            ("🔧", "Advanced", "Expert settings and debugging options"),
            ("☁️", "Cloud & Sync", "Online features and synchronization"),
            ("🎯", "Shortcuts", "Keyboard and mouse shortcuts"),
            ("📊", "Statistics", "Usage analytics and reporting")
        ]
        
        self.category_buttons = QButtonGroup()
        
        for i, (icon, title, desc) in enumerate(categories):
            btn = QPushButton(f"{icon} {title}")
            btn.setObjectName("categoryButton")
            btn.setCheckable(True)
            btn.setToolTip(desc)
            btn.clicked.connect(lambda checked, idx=i: self.switch_category(idx))
            
            if i == 0:  # Select first category by default
                btn.setChecked(True)
            
            self.category_buttons.addButton(btn, i)
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # Quick actions
        quick_frame = QFrame()
        quick_frame.setObjectName("quickActionsFrame")
        quick_layout = QVBoxLayout(quick_frame)
        
        quick_title = QLabel("⚡ Quick Actions")
        quick_title.setObjectName("quickTitle")
        quick_layout.addWidget(quick_title)
        
        # Reset button
        reset_btn = QPushButton("🔄 Reset All Settings")
        reset_btn.setObjectName("resetButton")
        reset_btn.clicked.connect(self.reset_all_settings)
        quick_layout.addWidget(reset_btn)
        
        # Export/Import
        export_btn = QPushButton("📤 Export Settings")
        export_btn.clicked.connect(self.export_settings)
        quick_layout.addWidget(export_btn)
        
        import_btn = QPushButton("📥 Import Settings")
        import_btn.clicked.connect(self.import_settings)
        quick_layout.addWidget(import_btn)
        
        layout.addWidget(quick_frame)
        
        return sidebar
    
    def create_all_tabs(self):
        """Create all settings tabs"""
        # Tab 0: Media & Playback
        media_tab = self.create_media_tab()
        self.content_stack.addTab(media_tab, "🎬 Media & Playback")
        
        # Tab 1: Audio System
        audio_tab = self.create_audio_tab()
        self.content_stack.addTab(audio_tab, "🔊 Audio System")
        
        # Tab 2: Appearance
        appearance_tab = self.create_appearance_tab()
        self.content_stack.addTab(appearance_tab, "🎨 Appearance")
        
        # Tab 3: Languages
        language_tab = self.create_language_tab()
        self.content_stack.addTab(language_tab, "🌐 Languages")
        
        # Tab 4: Performance
        performance_tab = self.create_performance_tab()
        self.content_stack.addTab(performance_tab, "⚡ Performance")
        
        # Tab 5: Privacy & Security
        privacy_tab = self.create_privacy_tab()
        self.content_stack.addTab(privacy_tab, "🔒 Privacy & Security")
        
        # Tab 6: Advanced
        advanced_tab = self.create_advanced_tab()
        self.content_stack.addTab(advanced_tab, "🔧 Advanced")
        
        # Tab 7: Cloud & Sync
        cloud_tab = self.create_cloud_tab()
        self.content_stack.addTab(cloud_tab, "☁️ Cloud & Sync")
        
        # Tab 8: Shortcuts
        shortcuts_tab = self.create_shortcuts_tab()
        self.content_stack.addTab(shortcuts_tab, "🎯 Shortcuts")
        
        # Tab 9: Statistics
        stats_tab = self.create_statistics_tab()
        self.content_stack.addTab(stats_tab, "📊 Statistics")
    
    def create_media_tab(self):
        """Create media and playback settings tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Video Settings Group
        video_group = QGroupBox("🎥 Video Settings")
        video_layout = QFormLayout(video_group)
        
        self.video_quality = QComboBox()
        self.video_quality.addItems(["Auto", "4K (2160p)", "1440p", "1080p", "720p", "480p"])
        video_layout.addRow("Default Quality:", self.video_quality)
        
        self.aspect_ratio = QComboBox()
        self.aspect_ratio.addItems(["Auto", "16:9", "4:3", "21:9", "1:1", "Custom"])
        video_layout.addRow("Aspect Ratio:", self.aspect_ratio)
        
        self.video_renderer = QComboBox()
        self.video_renderer.addItems(["Auto", "DirectX", "OpenGL", "Software"])
        video_layout.addRow("Video Renderer:", self.video_renderer)
        
        self.hardware_decode = QCheckBox("Enable hardware video decoding")
        self.hardware_decode.setChecked(True)
        video_layout.addRow("", self.hardware_decode)
        
        layout.addWidget(video_group)
        
        # Audio Settings Group
        audio_group = QGroupBox("🔊 Audio Settings")
        audio_layout = QFormLayout(audio_group)
        
        self.audio_quality = QComboBox()
        self.audio_quality.addItems(["Auto", "192 kbps", "320 kbps", "FLAC", "Hi-Res"])
        audio_layout.addRow("Audio Quality:", self.audio_quality)
        
        self.audio_channels = QComboBox()
        self.audio_channels.addItems(["Auto", "Stereo", "5.1", "7.1", "Mono"])
        audio_layout.addRow("Audio Channels:", self.audio_channels)
        
        self.sample_rate = QComboBox()
        self.sample_rate.addItems(["Auto", "44.1 kHz", "48 kHz", "96 kHz", "192 kHz"])
        audio_layout.addRow("Sample Rate:", self.sample_rate)
        
        layout.addWidget(audio_group)
        
        # Playback Control Group
        playback_group = QGroupBox("⏯️ Playback Control")
        playback_layout = QFormLayout(playback_group)
        
        self.skip_duration = QSpinBox()
        self.skip_duration.setRange(1, 300)
        self.skip_duration.setValue(10)
        self.skip_duration.setSuffix(" seconds")
        playback_layout.addRow("Skip Duration:", self.skip_duration)
        
        self.playback_speed = QComboBox()
        self.playback_speed.addItems(["0.25x", "0.5x", "0.75x", "1.0x", "1.25x", "1.5x", "2.0x", "Custom"])
        playback_layout.addRow("Default Speed:", self.playback_speed)
        
        self.auto_play = QCheckBox("Auto-play when file opens")
        playback_layout.addRow("", self.auto_play)
        
        self.remember_position = QCheckBox("Remember playback position")
        self.remember_position.setChecked(True)
        playback_layout.addRow("", self.remember_position)
        
        layout.addWidget(playback_group)
        
        layout.addStretch()
        tab.setWidget(content)
        return tab

    def create_language_tab(self):
        """Create language settings tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Interface Language Group
        interface_group = QGroupBox("🌐 Interface Language")
        interface_layout = QVBoxLayout(interface_group)

        # Language buttons with flags
        lang_layout = QHBoxLayout()
        self.language_buttons = QButtonGroup()

        languages = [
            ("🇺🇸 English", "en"),
            ("🇸🇦 العربية", "ar"),
            ("🇫🇷 Français", "fr"),
            ("🇪🇸 Español", "es"),
            ("🇩🇪 Deutsch", "de"),
            ("🇯🇵 日本語", "ja")
        ]

        for i, (name, code) in enumerate(languages):
            btn = QPushButton(name)
            btn.setCheckable(True)
            btn.setProperty("language_code", code)
            if i == 0:  # Default to English
                btn.setChecked(True)
            self.language_buttons.addButton(btn, i)
            lang_layout.addWidget(btn)

        interface_layout.addLayout(lang_layout)
        layout.addWidget(interface_group)

        # Content Language Group
        content_group = QGroupBox("📝 Content Language")
        content_layout = QFormLayout(content_group)

        self.subtitle_language = QComboBox()
        self.subtitle_language.addItems(["Auto-detect", "English", "Arabic", "French", "Spanish", "German"])
        content_layout.addRow("Default Subtitle Language:", self.subtitle_language)

        self.audio_language = QComboBox()
        self.audio_language.addItems(["Original", "English", "Arabic", "French", "Spanish"])
        content_layout.addRow("Preferred Audio Language:", self.audio_language)

        layout.addWidget(content_group)

        # Translation Settings Group
        translation_group = QGroupBox("🔄 Translation Settings")
        translation_layout = QFormLayout(translation_group)

        self.auto_translate = QCheckBox("Auto-translate subtitles")
        translation_layout.addRow("", self.auto_translate)

        self.translation_service = QComboBox()
        self.translation_service.addItems(["Offline (Argos)", "Google Translate", "Microsoft Translator"])
        translation_layout.addRow("Translation Service:", self.translation_service)

        layout.addWidget(translation_group)

        layout.addStretch()
        tab.setWidget(content)
        return tab

    def create_performance_tab(self):
        """Create performance settings tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Hardware Acceleration Group
        hardware_group = QGroupBox("⚡ Hardware Acceleration")
        hardware_layout = QVBoxLayout(hardware_group)

        self.gpu_acceleration = QCheckBox("🎮 Enable GPU acceleration")
        self.gpu_acceleration.setChecked(True)
        hardware_layout.addWidget(self.gpu_acceleration)

        self.multi_threading = QCheckBox("🔄 Multi-threaded processing")
        self.multi_threading.setChecked(True)
        hardware_layout.addWidget(self.multi_threading)

        self.hardware_decode = QCheckBox("📺 Hardware video decoding")
        self.hardware_decode.setChecked(True)
        hardware_layout.addWidget(self.hardware_decode)

        layout.addWidget(hardware_group)

        # Memory Management Group
        memory_group = QGroupBox("💾 Memory Management")
        memory_layout = QFormLayout(memory_group)

        self.cache_size = QSpinBox()
        self.cache_size.setRange(64, 4096)
        self.cache_size.setValue(512)
        self.cache_size.setSuffix(" MB")
        memory_layout.addRow("Cache Size:", self.cache_size)

        self.preload_next = QCheckBox("Preload next file in playlist")
        memory_layout.addRow("", self.preload_next)

        layout.addWidget(memory_group)

        layout.addStretch()
        tab.setWidget(content)
        return tab

    def create_privacy_tab(self):
        """Create privacy and security settings tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Privacy Group
        privacy_group = QGroupBox("🔒 Privacy Settings")
        privacy_layout = QVBoxLayout(privacy_group)

        self.usage_analytics = QCheckBox("📊 Send anonymous usage analytics")
        privacy_layout.addWidget(self.usage_analytics)

        self.crash_reports = QCheckBox("🐛 Send crash reports")
        privacy_layout.addWidget(self.crash_reports)

        self.remember_files = QCheckBox("💾 Remember recently opened files")
        self.remember_files.setChecked(True)
        privacy_layout.addWidget(self.remember_files)

        layout.addWidget(privacy_group)

        layout.addStretch()
        tab.setWidget(content)
        return tab

    def create_advanced_tab(self):
        """Create advanced settings tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Debug Group
        debug_group = QGroupBox("🔧 Debug Settings")
        debug_layout = QFormLayout(debug_group)

        self.debug_mode = QCheckBox("Enable debug mode")
        debug_layout.addRow("", self.debug_mode)

        self.log_level = QComboBox()
        self.log_level.addItems(["ERROR", "WARNING", "INFO", "DEBUG", "VERBOSE"])
        debug_layout.addRow("Log Level:", self.log_level)

        layout.addWidget(debug_group)

        layout.addStretch()
        tab.setWidget(content)
        return tab

    def create_cloud_tab(self):
        """Create cloud and sync settings tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Coming soon message
        coming_soon = QLabel("☁️ Cloud features coming in future updates!")
        coming_soon.setAlignment(Qt.AlignCenter)
        coming_soon.setStyleSheet("font-size: 18px; color: #7F8C8D; font-style: italic; padding: 50px;")
        layout.addWidget(coming_soon)

        layout.addStretch()
        tab.setWidget(content)
        return tab

    def create_shortcuts_tab(self):
        """Create keyboard shortcuts tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Shortcuts table
        shortcuts_group = QGroupBox("⌨️ Keyboard Shortcuts")
        shortcuts_layout = QVBoxLayout(shortcuts_group)

        self.shortcuts_table = QTableWidget(10, 2)
        self.shortcuts_table.setHorizontalHeaderLabels(["Action", "Shortcut"])

        # Add shortcuts
        shortcuts = [
            ("Play/Pause", "Spacebar"),
            ("Skip Forward", "Right Arrow"),
            ("Skip Backward", "Left Arrow"),
            ("Volume Up", "Up Arrow"),
            ("Volume Down", "Down Arrow"),
            ("Fullscreen", "F11"),
            ("Open File", "Ctrl+O"),
            ("Settings", "Ctrl+,"),
            ("Quit", "Ctrl+Q"),
            ("Mute", "M")
        ]

        for i, (action, shortcut) in enumerate(shortcuts):
            self.shortcuts_table.setItem(i, 0, QTableWidgetItem(action))
            self.shortcuts_table.setItem(i, 1, QTableWidgetItem(shortcut))

        shortcuts_layout.addWidget(self.shortcuts_table)
        layout.addWidget(shortcuts_group)

        layout.addStretch()
        tab.setWidget(content)
        return tab

    def create_statistics_tab(self):
        """Create statistics and analytics tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Usage Statistics Group
        stats_group = QGroupBox("📊 Usage Statistics")
        stats_layout = QFormLayout(stats_group)

        stats_layout.addRow("Total Play Time:", QLabel("0 hours"))
        stats_layout.addRow("Files Played:", QLabel("0"))
        stats_layout.addRow("Most Used Format:", QLabel("N/A"))
        stats_layout.addRow("Average Session:", QLabel("0 minutes"))

        layout.addWidget(stats_group)

        layout.addStretch()
        tab.setWidget(content)
        return tab

    def create_footer(self):
        """Create footer with action buttons"""
        footer = QFrame()
        footer.setObjectName("footerFrame")
        footer.setFixedHeight(70)

        layout = QHBoxLayout(footer)
        layout.setContentsMargins(30, 15, 30, 15)

        # Reset button
        self.reset_button = QPushButton("🔄 Reset to Defaults")
        self.reset_button.setObjectName("resetButton")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        layout.addWidget(self.reset_button)

        layout.addStretch()

        # Action buttons
        self.cancel_button = QPushButton("❌ Cancel")
        self.cancel_button.setObjectName("cancelButton")
        self.cancel_button.clicked.connect(self.reject)
        layout.addWidget(self.cancel_button)

        self.apply_button = QPushButton("✅ Apply")
        self.apply_button.setObjectName("applyButton")
        self.apply_button.clicked.connect(self.apply_settings)
        layout.addWidget(self.apply_button)

        self.ok_button = QPushButton("💾 OK")
        self.ok_button.setObjectName("okButton")
        self.ok_button.clicked.connect(self.accept_settings)
        layout.addWidget(self.ok_button)

        return footer

    # Event handlers and utility methods
    def switch_category(self, index):
        """Switch to selected category"""
        self.content_stack.setCurrentIndex(index)

    def quick_theme_change(self, theme_name):
        """Handle quick theme change"""
        self.theme_changed.emit(theme_name)

    def quick_language_change(self, language_name):
        """Handle quick language change"""
        # Extract language code from the selection
        lang_codes = {
            "🇺🇸 English": "en",
            "🇸🇦 العربية": "ar",
            "🇫🇷 Français": "fr",
            "🇪🇸 Español": "es"
        }

        lang_code = lang_codes.get(language_name, "en")
        self.settings["interface_language"] = lang_code

        # Update UI language (placeholder for now)
        self.status_bar_message = f"Language changed to: {language_name}"

    def choose_color(self, color_type):
        """Open color picker"""
        color = QColorDialog.getColor(QColor("#667eea"), self)
        if color.isValid():
            button = getattr(self, f"{color_type}_color")
            button.setStyleSheet(f"background-color: {color.name()}; color: white;")

    def load_settings(self):
        """Load settings from file"""
        # Default settings
        return {
            "theme": "Professional",
            "language": "en",
            "volume": 70,
            "hardware_acceleration": True
        }

    def load_current_settings(self):
        """Load current settings into UI"""
        pass  # Implementation here

    def apply_settings(self):
        """Apply settings without closing"""
        self.settings_changed.emit(self.settings)

    def accept_settings(self):
        """Accept and apply settings"""
        self.apply_settings()
        self.accept()

    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        pass  # Implementation here

    def export_settings(self):
        """Export settings to file"""
        pass  # Implementation here

    def import_settings(self):
        """Import settings from file"""
        pass  # Implementation here

    def reset_all_settings(self):
        """Reset all settings"""
        pass  # Implementation here

    def setup_animations(self):
        """Setup smooth animations"""
        pass  # Implementation here

    def apply_professional_styles(self):
        """Apply professional styling"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                color: #ffffff;
            }

            #headerFrame {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }

            #headerTitle {
                font-size: 24px;
                font-weight: 700;
                color: #ffffff;
            }

            #headerSubtitle {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
            }

            #sidebarFrame {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(30px);
                border-right: 1px solid rgba(255, 255, 255, 0.2);
            }

            #categoryButton {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                padding: 15px;
                text-align: left;
                font-weight: 600;
                color: #ffffff;
            }

            #categoryButton:checked {
                background: rgba(255, 255, 255, 0.25);
                border-color: rgba(255, 255, 255, 0.4);
            }

            #footerFrame {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-top: 1px solid rgba(255, 255, 255, 0.2);
            }

            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 10px 20px;
                font-weight: 600;
                color: #ffffff;
            }

            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.5);
            }

            QGroupBox {
                font-weight: 600;
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                margin: 15px 0;
                padding-top: 15px;
                color: #ffffff;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                font-size: 14px;
                font-weight: 700;
            }
        """)
    
    def create_audio_tab(self):
        """Create advanced audio settings tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Audio Output Group
        output_group = QGroupBox("🔊 Audio Output")
        output_layout = QFormLayout(output_group)
        
        self.audio_device = QComboBox()
        self.audio_device.addItems(["Default", "Speakers", "Headphones", "HDMI", "Bluetooth"])
        output_layout.addRow("Output Device:", self.audio_device)
        
        self.exclusive_mode = QCheckBox("Exclusive mode (WASAPI)")
        output_layout.addRow("", self.exclusive_mode)
        
        self.audio_buffer = QSpinBox()
        self.audio_buffer.setRange(64, 2048)
        self.audio_buffer.setValue(512)
        self.audio_buffer.setSuffix(" samples")
        output_layout.addRow("Buffer Size:", self.audio_buffer)
        
        layout.addWidget(output_group)
        
        # Audio Effects Group
        effects_group = QGroupBox("🎚️ Audio Effects")
        effects_layout = QVBoxLayout(effects_group)
        
        # Equalizer presets
        eq_layout = QHBoxLayout()
        eq_label = QLabel("Equalizer Preset:")
        self.eq_preset = QComboBox()
        self.eq_preset.addItems(["Flat", "Rock", "Pop", "Jazz", "Classical", "Electronic", "Custom"])
        eq_layout.addWidget(eq_label)
        eq_layout.addWidget(self.eq_preset)
        effects_layout.addLayout(eq_layout)
        
        # Audio effects checkboxes
        self.bass_boost = QCheckBox("🎵 Bass Boost")
        self.treble_enhance = QCheckBox("🎼 Treble Enhancement")
        self.surround_sound = QCheckBox("🔊 Virtual Surround Sound")
        self.noise_reduction = QCheckBox("🔇 Noise Reduction")
        self.voice_clarity = QCheckBox("🎤 Voice Clarity Enhancement")
        self.dynamic_range = QCheckBox("📊 Dynamic Range Compression")
        
        effects_layout.addWidget(self.bass_boost)
        effects_layout.addWidget(self.treble_enhance)
        effects_layout.addWidget(self.surround_sound)
        effects_layout.addWidget(self.noise_reduction)
        effects_layout.addWidget(self.voice_clarity)
        effects_layout.addWidget(self.dynamic_range)
        
        layout.addWidget(effects_group)
        
        # Volume Control Group
        volume_group = QGroupBox("🔊 Volume Control")
        volume_layout = QFormLayout(volume_group)
        
        self.default_volume = QSlider(Qt.Horizontal)
        self.default_volume.setRange(0, 100)
        self.default_volume.setValue(70)
        self.volume_display = QLabel("70%")
        self.default_volume.valueChanged.connect(lambda v: self.volume_display.setText(f"{v}%"))
        
        volume_h_layout = QHBoxLayout()
        volume_h_layout.addWidget(self.default_volume)
        volume_h_layout.addWidget(self.volume_display)
        volume_layout.addRow("Default Volume:", volume_h_layout)
        
        self.volume_boost = QCheckBox("Enable volume boost (up to 200%)")
        volume_layout.addRow("", self.volume_boost)
        
        self.volume_normalization = QCheckBox("Auto volume normalization")
        volume_layout.addRow("", self.volume_normalization)
        
        layout.addWidget(volume_group)
        
        layout.addStretch()
        tab.setWidget(content)
        return tab
    
    def create_appearance_tab(self):
        """Create appearance and theme settings tab"""
        tab = QScrollArea()
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Theme Selection Group
        theme_group = QGroupBox("🎨 Theme Selection")
        theme_layout = QVBoxLayout(theme_group)
        
        # Theme radio buttons
        self.theme_buttons = QButtonGroup()
        
        themes = [
            ("🌟 Professional", "Modern glassmorphism with gradients"),
            ("🌙 Dark Mode", "Classic dark theme for night use"),
            ("☀️ Light Mode", "Clean light theme for day use"),
            ("🎨 Custom", "Create your own custom theme")
        ]
        
        for i, (name, desc) in enumerate(themes):
            radio = QRadioButton(name)
            radio.setToolTip(desc)
            if i == 0:  # Default to Professional
                radio.setChecked(True)
            self.theme_buttons.addButton(radio, i)
            theme_layout.addWidget(radio)
        
        layout.addWidget(theme_group)
        
        # Color Customization Group
        color_group = QGroupBox("🌈 Color Customization")
        color_layout = QFormLayout(color_group)
        
        self.primary_color = QPushButton("Choose Primary Color")
        self.primary_color.setStyleSheet("background-color: #667eea; color: white;")
        self.primary_color.clicked.connect(lambda: self.choose_color("primary"))
        color_layout.addRow("Primary Color:", self.primary_color)
        
        self.accent_color = QPushButton("Choose Accent Color")
        self.accent_color.setStyleSheet("background-color: #764ba2; color: white;")
        self.accent_color.clicked.connect(lambda: self.choose_color("accent"))
        color_layout.addRow("Accent Color:", self.accent_color)
        
        self.background_color = QPushButton("Choose Background")
        self.background_color.setStyleSheet("background-color: #f8f9fa; color: black;")
        self.background_color.clicked.connect(lambda: self.choose_color("background"))
        color_layout.addRow("Background:", self.background_color)
        
        layout.addWidget(color_group)
        
        # UI Customization Group
        ui_group = QGroupBox("🖼️ Interface Customization")
        ui_layout = QFormLayout(ui_group)
        
        self.ui_scale = QSlider(Qt.Horizontal)
        self.ui_scale.setRange(80, 150)
        self.ui_scale.setValue(100)
        self.scale_display = QLabel("100%")
        self.ui_scale.valueChanged.connect(lambda v: self.scale_display.setText(f"{v}%"))
        
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(self.ui_scale)
        scale_layout.addWidget(self.scale_display)
        ui_layout.addRow("UI Scale:", scale_layout)
        
        self.font_family = QComboBox()
        self.font_family.addItems(["Segoe UI", "Arial", "Helvetica", "SF Pro Display", "Roboto"])
        ui_layout.addRow("Font Family:", self.font_family)
        
        self.animations = QCheckBox("Enable smooth animations")
        self.animations.setChecked(True)
        ui_layout.addRow("", self.animations)
        
        self.transparency = QCheckBox("Enable window transparency")
        ui_layout.addRow("", self.transparency)
        
        layout.addWidget(ui_group)
        
        layout.addStretch()
        tab.setWidget(content)
        return tab
